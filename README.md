# Geeks NEXTJS

Geeks UI Kit is built with the most popular front-end framework [React Bootstrap](https://react-bootstrap.github.io/) latest with react-scripts and nextjs framework (https://nextjs.org/).

## Getting Started

### Running in Local environment
**1. Requirements Node.js**

Before proceeding you'll need to have the latest stable nodejs. Install [Node.js](https://nodejs.org/en/download/) or already have it installed on your machine move to next step.

**2. Install Geeks Nextjs**

Open the "geeksnextjs” directory with your cmd or terminal. Open the project folder and install its dependencies.

```
cd geeksnextjs 
```
```
npm install 
```
This command will download all the necessary dependencies for geeks in the node_modules directory.

**3. Start**

Once the installation is done, you can now start your app by running `npm run dev` command in development mode. A local web server will start at [http://localhost:3000](http://localhost:3000)


### Creating a Production Build.

Production build of your app.

1. Run `npm run build` command in your project directory to make the Production build app.
```
npm run build
```
2. Open Static Server for the production build
```
npm run start
```

## Deploy on Vercel

There are different ways to deploy your project on vercel, here is the documentation of [Vercel Platform](https://vercel.com/docs/concepts/deployments/overview) from the creators of NextJs.

For more details to [Unlock the full potential of Next.js](https://vercel.com/solutions/nextjs).

## Support

Codescandy is happy to provide support for issues. Contact us an <NAME_EMAIL>
# company
