// import node module libraries
import { useState, useEffect } from 'react';
import { Card, Button, Form, Row, Col, Alert, ProgressBar } from 'react-bootstrap';
import { ChevronLeft, ChevronRight, Check } from 'react-bootstrap-icons';

// import appwrite service
import { appwriteService } from 'services/appwrite';

const InvestorRegistrationForm = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    nationality: '',
    
    // Professional Information
    occupation: '',
    company: '',
    jobTitle: '',
    workExperience: '',
    
    // Investment Information
    investmentExperience: '',
    investmentRange: '',
    riskTolerance: '',
    investmentGoals: '',
    preferredSectors: [],
    
    // Financial Information
    annualIncome: '',
    netWorth: '',
    liquidAssets: '',
    
    // Additional Information
    hearAboutUs: '',
    additionalComments: '',
    agreeToTerms: false,
    agreeToMarketing: false
  });

  const steps = [
    {
      title: "Personal Information",
      subtitle: "Let's start with your basic details",
      fields: ['firstName', 'lastName', 'email', 'phone', 'dateOfBirth', 'nationality']
    },
    {
      title: "Professional Background",
      subtitle: "Tell us about your professional experience",
      fields: ['occupation', 'company', 'jobTitle', 'workExperience']
    },
    {
      title: "Investment Experience",
      subtitle: "Help us understand your investment background",
      fields: ['investmentExperience', 'investmentRange', 'riskTolerance', 'investmentGoals']
    },
    {
      title: "Investment Preferences",
      subtitle: "What sectors interest you most?",
      fields: ['preferredSectors']
    },
    {
      title: "Financial Information",
      subtitle: "This helps us ensure regulatory compliance",
      fields: ['annualIncome', 'netWorth', 'liquidAssets']
    },
    {
      title: "Final Details",
      subtitle: "Just a few more questions to complete your registration",
      fields: ['hearAboutUs', 'additionalComments', 'agreeToTerms', 'agreeToMarketing']
    }
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name === 'preferredSectors') {
      const currentSectors = formData.preferredSectors;
      if (checked) {
        setFormData(prev => ({
          ...prev,
          preferredSectors: [...currentSectors, value]
        }));
      } else {
        setFormData(prev => ({
          ...prev,
          preferredSectors: currentSectors.filter(sector => sector !== value)
        }));
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const validateStep = (stepIndex) => {
    const stepFields = steps[stepIndex].fields;
    const requiredFields = stepFields.filter(field => 
      field !== 'additionalComments' && field !== 'agreeToMarketing'
    );
    
    return requiredFields.every(field => {
      if (field === 'preferredSectors') {
        return formData[field].length > 0;
      }
      if (field === 'agreeToTerms') {
        return formData[field] === true;
      }
      return formData[field] && formData[field].toString().trim() !== '';
    });
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length - 1));
      setError('');
    } else {
      setError('Please fill in all required fields before proceeding.');
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
    setError('');
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) {
      setError('Please fill in all required fields.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await appwriteService.createInvestorRegistration(formData);
      setSuccess(true);
    } catch (error) {
      setError(error.message || 'Failed to submit registration. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const progress = ((currentStep + 1) / steps.length) * 100;

  if (success) {
    return (
      <Card className="form-step-card border-0 shadow-lg">
        <Card.Body className="success-container">
          <div className="mb-4">
            <div className="success-icon rounded-circle d-inline-flex align-items-center justify-content-center"
                 style={{ width: '80px', height: '80px' }}>
              <Check size={40} className="text-white" />
            </div>
          </div>
          <h2 className="mb-3">Registration Submitted Successfully!</h2>
          <p className="text-muted mb-4">
            Thank you for your interest in investing with Sharif Ventures.
            Our team will review your application and contact you within 2-3 business days.
          </p>
          <Button variant="primary" href="/" className="px-4">
            Return to Home
          </Button>
        </Card.Body>
      </Card>
    );
  }

  return (
    <Card className="form-step-card border-0 shadow-lg">
      <Card.Body className="p-0">
        {/* Progress Bar */}
        <div className="p-4 border-bottom">
          <div className="d-flex justify-content-between align-items-center mb-2">
            <small className="progress-indicator">Step {currentStep + 1} of {steps.length}</small>
            <small className="progress-indicator">{Math.round(progress)}% Complete</small>
          </div>
          <div className="progress-bar-container">
            <ProgressBar now={progress} className="mb-0" style={{ height: '4px' }} />
          </div>
        </div>

        {/* Form Content */}
        <div className="p-5">
          <div className="mb-4 step-content">
            <h3 className="step-title">{steps[currentStep].title}</h3>
            <p className="step-subtitle">{steps[currentStep].subtitle}</p>
          </div>

          {error && (
            <Alert variant="danger" className="mb-4">
              {error}
            </Alert>
          )}

          <Form>
            {/* Step 0: Personal Information */}
            {currentStep === 0 && (
              <Row>
                <Col md={6} className="mb-3">
                  <Form.Label>First Name *</Form.Label>
                  <Form.Control
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    placeholder="Enter your first name"
                    required
                  />
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Label>Last Name *</Form.Label>
                  <Form.Control
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    placeholder="Enter your last name"
                    required
                  />
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Label>Email Address *</Form.Label>
                  <Form.Control
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter your email address"
                    required
                  />
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Label>Phone Number *</Form.Label>
                  <Form.Control
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="Enter your phone number"
                    required
                  />
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Label>Date of Birth *</Form.Label>
                  <Form.Control
                    type="date"
                    name="dateOfBirth"
                    value={formData.dateOfBirth}
                    onChange={handleInputChange}
                    required
                  />
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Label>Nationality *</Form.Label>
                  <Form.Control
                    type="text"
                    name="nationality"
                    value={formData.nationality}
                    onChange={handleInputChange}
                    placeholder="Enter your nationality"
                    required
                  />
                </Col>
              </Row>
            )}

            {/* Step 1: Professional Background */}
            {currentStep === 1 && (
              <Row>
                <Col md={6} className="mb-3">
                  <Form.Label>Occupation *</Form.Label>
                  <Form.Select
                    name="occupation"
                    value={formData.occupation}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select your occupation</option>
                    <option value="business_owner">Business Owner</option>
                    <option value="executive">Executive</option>
                    <option value="professional">Professional</option>
                    <option value="entrepreneur">Entrepreneur</option>
                    <option value="investor">Investor</option>
                    <option value="retired">Retired</option>
                    <option value="other">Other</option>
                  </Form.Select>
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Label>Company *</Form.Label>
                  <Form.Control
                    type="text"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    placeholder="Enter your company name"
                    required
                  />
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Label>Job Title *</Form.Label>
                  <Form.Control
                    type="text"
                    name="jobTitle"
                    value={formData.jobTitle}
                    onChange={handleInputChange}
                    placeholder="Enter your job title"
                    required
                  />
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Label>Years of Work Experience *</Form.Label>
                  <Form.Select
                    name="workExperience"
                    value={formData.workExperience}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select experience level</option>
                    <option value="0-2">0-2 years</option>
                    <option value="3-5">3-5 years</option>
                    <option value="6-10">6-10 years</option>
                    <option value="11-15">11-15 years</option>
                    <option value="16-20">16-20 years</option>
                    <option value="20+">20+ years</option>
                  </Form.Select>
                </Col>
              </Row>
            )}

            {/* Step 2: Investment Experience */}
            {currentStep === 2 && (
              <Row>
                <Col md={6} className="mb-3">
                  <Form.Label>Investment Experience *</Form.Label>
                  <Form.Select
                    name="investmentExperience"
                    value={formData.investmentExperience}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select your experience level</option>
                    <option value="beginner">Beginner (0-2 years)</option>
                    <option value="intermediate">Intermediate (3-7 years)</option>
                    <option value="experienced">Experienced (8-15 years)</option>
                    <option value="expert">Expert (15+ years)</option>
                  </Form.Select>
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Label>Investment Range *</Form.Label>
                  <Form.Select
                    name="investmentRange"
                    value={formData.investmentRange}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select investment range</option>
                    <option value="10k-50k">$10,000 - $50,000</option>
                    <option value="50k-100k">$50,000 - $100,000</option>
                    <option value="100k-250k">$100,000 - $250,000</option>
                    <option value="250k-500k">$250,000 - $500,000</option>
                    <option value="500k-1m">$500,000 - $1,000,000</option>
                    <option value="1m+">$1,000,000+</option>
                  </Form.Select>
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Label>Risk Tolerance *</Form.Label>
                  <Form.Select
                    name="riskTolerance"
                    value={formData.riskTolerance}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select risk tolerance</option>
                    <option value="conservative">Conservative</option>
                    <option value="moderate">Moderate</option>
                    <option value="aggressive">Aggressive</option>
                    <option value="very_aggressive">Very Aggressive</option>
                  </Form.Select>
                </Col>
                <Col md={6} className="mb-3">
                  <Form.Label>Investment Goals *</Form.Label>
                  <Form.Select
                    name="investmentGoals"
                    value={formData.investmentGoals}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select primary goal</option>
                    <option value="capital_growth">Capital Growth</option>
                    <option value="income_generation">Income Generation</option>
                    <option value="portfolio_diversification">Portfolio Diversification</option>
                    <option value="supporting_innovation">Supporting Innovation</option>
                    <option value="social_impact">Social Impact</option>
                  </Form.Select>
                </Col>
              </Row>
            )}

            {/* Step 3: Investment Preferences */}
            {currentStep === 3 && (
              <div>
                <Form.Label className="mb-3">Preferred Investment Sectors * (Select all that apply)</Form.Label>
                <Row>
                  {[
                    'Technology', 'Healthcare', 'Fintech', 'E-commerce', 'SaaS',
                    'AI/ML', 'Blockchain', 'Clean Energy', 'Food & Beverage',
                    'Education', 'Real Estate', 'Manufacturing'
                  ].map((sector) => (
                    <Col md={4} key={sector} className="mb-2">
                      <Form.Check
                        type="checkbox"
                        name="preferredSectors"
                        value={sector}
                        checked={formData.preferredSectors.includes(sector)}
                        onChange={handleInputChange}
                        label={sector}
                      />
                    </Col>
                  ))}
                </Row>
              </div>
            )}

            {/* Step 4: Financial Information */}
            {currentStep === 4 && (
              <Row>
                <Col md={12} className="mb-4">
                  <Alert variant="info">
                    <small>
                      This information is required for regulatory compliance and will be kept strictly confidential.
                    </small>
                  </Alert>
                </Col>
                <Col md={4} className="mb-3">
                  <Form.Label>Annual Income *</Form.Label>
                  <Form.Select
                    name="annualIncome"
                    value={formData.annualIncome}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select income range</option>
                    <option value="under_100k">Under $100,000</option>
                    <option value="100k-250k">$100,000 - $250,000</option>
                    <option value="250k-500k">$250,000 - $500,000</option>
                    <option value="500k-1m">$500,000 - $1,000,000</option>
                    <option value="1m+">$1,000,000+</option>
                  </Form.Select>
                </Col>
                <Col md={4} className="mb-3">
                  <Form.Label>Net Worth *</Form.Label>
                  <Form.Select
                    name="netWorth"
                    value={formData.netWorth}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select net worth range</option>
                    <option value="under_500k">Under $500,000</option>
                    <option value="500k-1m">$500,000 - $1,000,000</option>
                    <option value="1m-5m">$1,000,000 - $5,000,000</option>
                    <option value="5m-10m">$5,000,000 - $10,000,000</option>
                    <option value="10m+">$10,000,000+</option>
                  </Form.Select>
                </Col>
                <Col md={4} className="mb-3">
                  <Form.Label>Liquid Assets *</Form.Label>
                  <Form.Select
                    name="liquidAssets"
                    value={formData.liquidAssets}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select liquid assets range</option>
                    <option value="under_100k">Under $100,000</option>
                    <option value="100k-500k">$100,000 - $500,000</option>
                    <option value="500k-1m">$500,000 - $1,000,000</option>
                    <option value="1m-5m">$1,000,000 - $5,000,000</option>
                    <option value="5m+">$5,000,000+</option>
                  </Form.Select>
                </Col>
              </Row>
            )}

            {/* Step 5: Final Details */}
            {currentStep === 5 && (
              <Row>
                <Col md={12} className="mb-3">
                  <Form.Label>How did you hear about us? *</Form.Label>
                  <Form.Select
                    name="hearAboutUs"
                    value={formData.hearAboutUs}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select an option</option>
                    <option value="search_engine">Search Engine</option>
                    <option value="social_media">Social Media</option>
                    <option value="referral">Referral from friend/colleague</option>
                    <option value="industry_event">Industry Event</option>
                    <option value="news_article">News Article</option>
                    <option value="linkedin">LinkedIn</option>
                    <option value="other">Other</option>
                  </Form.Select>
                </Col>
                <Col md={12} className="mb-4">
                  <Form.Label>Additional Comments</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={4}
                    name="additionalComments"
                    value={formData.additionalComments}
                    onChange={handleInputChange}
                    placeholder="Any additional information you'd like to share..."
                  />
                </Col>
                <Col md={12} className="mb-3">
                  <Form.Check
                    type="checkbox"
                    name="agreeToTerms"
                    checked={formData.agreeToTerms}
                    onChange={handleInputChange}
                    label={
                      <span>
                        I agree to the{' '}
                        <a href="/terms" target="_blank" rel="noopener noreferrer">
                          Terms of Service
                        </a>{' '}
                        and{' '}
                        <a href="/privacy" target="_blank" rel="noopener noreferrer">
                          Privacy Policy
                        </a>{' '}
                        *
                      </span>
                    }
                    required
                  />
                </Col>
                <Col md={12} className="mb-3">
                  <Form.Check
                    type="checkbox"
                    name="agreeToMarketing"
                    checked={formData.agreeToMarketing}
                    onChange={handleInputChange}
                    label="I would like to receive updates about investment opportunities and company news"
                  />
                </Col>
              </Row>
            )}
          </Form>
        </div>

        {/* Navigation */}
        <div className="navigation-buttons p-4 d-flex justify-content-between">
          <Button 
            variant="outline-secondary" 
            onClick={prevStep}
            disabled={currentStep === 0}
            className="d-flex align-items-center"
          >
            <ChevronLeft size={16} className="me-1" />
            Previous
          </Button>
          
          {currentStep === steps.length - 1 ? (
            <Button 
              variant="primary" 
              onClick={handleSubmit}
              disabled={loading}
              className="d-flex align-items-center"
            >
              {loading ? 'Submitting...' : 'Submit Registration'}
            </Button>
          ) : (
            <Button 
              variant="primary" 
              onClick={nextStep}
              className="d-flex align-items-center"
            >
              Next
              <ChevronRight size={16} className="ms-1" />
            </Button>
          )}
        </div>
      </Card.Body>
    </Card>
  );
};

export default InvestorRegistrationForm;
