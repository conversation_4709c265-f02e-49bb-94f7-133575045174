{"name": "geeks-nextjs", "version": "2.2.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "now-build": "next build"}, "dependencies": {"@fullcalendar/bootstrap5": "^6.1.10", "@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/list": "^6.1.10", "@fullcalendar/react": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@mdi/js": "^7.4.47", "@mdi/react": "^1.6.1", "@pathofdev/react-tag-input": "^1.0.7", "@reduxjs/toolkit": "^2.1.0", "@tanstack/react-table": "^8.11.7", "@tippyjs/react": "^4.2.6", "apexcharts": "^3.45.2", "appwrite": "^15.0.0", "bootstrap": "^5.3.2", "caniuse-lite": "", "formik": "^2.4.5", "next": "^14.1.0", "next-seo": "^6.4.0", "nouislider-react": "^3.4.2", "prism-react-renderer": "^2.3.1", "quill": "^1.3.7", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.10.0", "react-bootstrap-icons": "^1.10.3", "react-copy-to-clipboard": "^5.1.0", "react-data-table-component": "^7.6.2", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-feather": "^2.0.10", "react-flatpickr": "^3.10.13", "react-input-mask": "^2.0.4", "react-modal-video": "^2.0.1", "react-moment": "^1.1.3", "react-odometerjs": "^3.1.0", "react-paginate": "^8.2.0", "react-quilljs": "^1.3.3", "react-redux": "^9.1.0", "react-responsive": "^9.0.2", "react-simple-maps": "^3.0.0", "react-simple-typewriter": "^5.0.1", "react-slick": "^0.30.1", "react-to-print": "^2.14.15", "react-toastify": "^10.0.4", "react-youtube": "^10.1.0", "redux": "^5.0.1", "sass": "^1.70.0", "simplebar": "^6.2.5", "simplebar-react": "^3.2.4", "styled-components": "^6.1.8", "uuid": "^9.0.1", "yet-another-react-lightbox": "^3.16.0", "yup": "^1.3.3", "zxcvbn": "^4.4.2"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.1.0"}}