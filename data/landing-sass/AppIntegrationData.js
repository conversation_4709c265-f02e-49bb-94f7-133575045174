import { v4 as uuid } from 'uuid';

export const AppIntegrationData = [
	{
		id: uuid(),
		applogo: '/images/brand/dropbox-logo.svg',
		appname: 'Dropbox',
		content: 'Bring your files and cloud content together.'
	},
	{
		id: uuid(),
		applogo: '/images/brand/hubspot.svg',
		appname: 'HubSpot',
		content: 'Full platform of marketing, sales, other service.'
	},
	{
		id: uuid(),
		applogo: '/images/brand/google-drive.svg',
		appname: 'Drive',
		content: 'Integrates seamlessly with Docs, Sheets...'
	},
	{
		id: uuid(),
		applogo: '/images/brand/slack-logo.svg',
		appname: 'Slack',
		content: 'New way to communicate with your team'
	},
	{
		id: uuid(),
		applogo: '/images/brand/zapier.svg',
		appname: 'Zapier',
		content: 'Streamline work with automation today.'
	},
	{
		id: uuid(),
		applogo: '/images/brand/hubspot.svg',
		appname: 'HubSpot',
		content: 'Full platform of marketing,sales, other service.'
	}
];

export default AppIntegrationData;
