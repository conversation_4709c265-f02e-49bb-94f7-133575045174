export const filesdata = [
	{
		id: 1,
		filename: 'Admin-Dashboard-Design.xlsx',
		filesize: '2.3MB',
		date_modified: '27 Sept, 2021',
		avatar: '/images/avatar/avatar-1.jpg',
		uploadedby: '<PERSON>',
		action: 1
	},
	{
		id: 2,
		filename: 'Admin-Dashboard-Design.jpeg',
		filesize: '10MB',
		date_modified: '29 Sept, 2021',
		avatar: '/images/avatar/avatar-2.jpg',
		uploadedby: '<PERSON><PERSON>',
		action: 1
	},
	{
		id: 3,
		filename: 'Geeks UI Description.doc',
		filesize: '45MB',
		date_modified: '01 Oct, 2021',
		avatar: null,
		uploadedby: '<PERSON>',
		action: 1
	},
	{
		id: 4,
		filename: 'Geeks-UI-Marketing-Process.ppt',
		filesize: '122MB',
		date_modified: '04 Oct, 2021',
		avatar: '/images/avatar/avatar-3.jpg',
		uploadedby: '<PERSON><PERSON>',
		action: 1
	},
	{
		id: 5,
		filename: 'Geeks-UI-Marketing-Process.txt',
		filesize: '43.5MB',
		date_modified: '06 Oct, 2021',
		avatar: null,
		uploadedby: '<PERSON><PERSON>',
		action: 1
	},
	{
		id: 6,
		filename: '<PERSON>ks-UI-promo-trailer-template.mov',
		filesize: '115MB',
		date_modified: '08 Oct, 2021',
		avatar: '/images/avatar/avatar-12.jpg',
		uploadedby: 'Jacob Jones',
		action: 1
	},
	{
		id: 7,
		filename: 'Earning-from-Dashboard.xlsx',
		filesize: '55MB',
		date_modified: '12 Oct, 2021',
		avatar: '/images/avatar/avatar-6.jpg',
		uploadedby: 'Kristin Watson',
		action: 1
	},
	{
		id: 8,
		filename: 'Geeks-UI-Marketing-Process.ppt',
		filesize: '122MB',
		date_modified: '04 Oct, 2021',
		avatar: null,
		uploadedby: 'Dianna Smiley',
		action: 1
	},
	{
		id: 9,
		filename: 'Geeks-UI-Marketing-Process.txt',
		filesize: '43.5MB',
		date_modified: '06 Oct, 2021',
		avatar: '/images/avatar/avatar-13.jpg',
		uploadedby: 'Xiaon Merry',
		action: 1
	},
	{
		id: 10,
		filename: 'Admin-Dashboard-Design.jpeg',
		filesize: '10MB',
		date_modified: '16 Nov, 2020',
		avatar: '/images/avatar/avatar-14.jpg',
		uploadedby: 'Rivao Luke',
		action: 1
	},
	{
		id: 11,
		filename: 'Admin-Dashboard-Design.jpeg',
		filesize: '10MB',
		date_modified: '29 Sept, 2021',
		avatar: '/images/avatar/avatar-15.jpg',
		uploadedby: 'Dianna Smiley',
		action: 1
	},
	{
		id: 12,
		filename: 'Admin-Dashboard-Design.xlsx',
		filesize: '55MB',
		date_modified: '16 Nov, 2020',
		avatar: '/images/avatar/avatar-17.jpg',
		uploadedby: 'Nia Sikhone',
		action: 1
	},
	{
		id: 13,
		filename: 'Geeks-UI-Marketing-Process.ppt',
		filesize: '122MB',
		date_modified: '04 Oct, 2021',
		avatar: '/images/avatar/avatar-18.jpg',
		uploadedby: 'Kristin Watson',
		action: 1
	},
	{
		id: 14,
		filename: 'Geeks UI Description.doc',
		filesize: '45MB',
		date_modified: '01 Oct, 2021',
		avatar: '/images/avatar/avatar-1.jpg',
		uploadedby: 'Paul Haney',
		action: 1
	},
	{
		id: 15,
		filename: 'Earning-from-Dashboard.xlsx',
		filesize: '55MB',
		date_modified: '16 Nov, 2020',
		avatar: '/images/avatar/avatar-2.jpg',
		uploadedby: 'Dianna Smiley',
		action: 1
	},
	{
		id: 16,
		filename: 'Admin-Dashboard-Design.jpeg',
		filesize: '10MB',
		date_modified: '16 Nov, 2020',
		avatar: '/images/avatar/avatar-3.jpg',
		uploadedby: 'Kristin Watson',
		action: 1
	},
	{
		id: 17,
		filename: 'Earning-from-Dashboard.xlsx',
		filesize: '55MB',
		date_modified: '29 Sept, 2021',
		avatar: '/images/avatar/avatar-4.jpg',
		uploadedby: 'Nia Sikhone',
		action: 1
	},
	{
		id: 18,
		filename: 'Geeks-UI-Marketing-Process.txt',
		filesize: '43.5MB',
		date_modified: '06 Oct, 2021',
		avatar: '/images/avatar/avatar-6.jpg',
		uploadedby: 'Kristin Watson',
		action: 1
	},
	{
		id: 19,
		filename: 'Geeks-UI-Marketing-Process.ppt',
		filesize: '122MB',
		date_modified: '04 Oct, 2021',
		avatar: '/images/avatar/avatar-7.jpg',
		uploadedby: 'Dianna Smiley',
		action: 1
	},
	{
		id: 20,
		filename: 'Admin-Dashboard-Design.xlsx',
		filesize: '55MB',
		date_modified: '16 Nov, 2020',
		avatar: '/images/avatar/avatar-12.jpg',
		uploadedby: 'Jacob Jones',
		action: 1
	},
	{
		id: 21,
		filename: 'Admin-Dashboard-Design.jpeg',
		filesize: '10MB',
		date_modified: '29 Sept, 2021',
		avatar: '/images/avatar/avatar-13.jpg',
		uploadedby: 'Xiaon Merry',
		action: 1
	},
	{
		id: 22,
		filename: 'Geeks UI Description.doc',
		filesize: '45MB',
		date_modified: '01 Oct, 2021',
		avatar: '/images/avatar/avatar-14.jpg',
		uploadedby: 'Rivao Luke',
		action: 1
	},
	{
		id: 23,
		filename: 'Geeks-UI-Marketing-Process.ppt',
		filesize: '122MB',
		date_modified: '04 Oct, 2021',
		avatar: '/images/avatar/avatar-15.jpg',
		uploadedby: 'Dianna Smiley',
		action: 1
	},
	{
		id: 24,
		filename: 'Admin-Dashboard-Design.xlsx',
		filesize: '55MB',
		date_modified: '16 Nov, 2020',
		avatar: '/images/avatar/avatar-17.jpg',
		uploadedby: 'Nia Sikhone',
		action: 1
	},
	{
		id: 25,
		filename: 'Geeks-UI-promo-trailer-template.mov',
		filesize: '115MB',
		date_modified: '08 Oct, 2021',
		avatar: '/images/avatar/avatar-18.jpg',
		uploadedby: 'Kristin Watson',
		action: 1
	},
	{
		id: 26,
		filename: 'Geeks-UI-Marketing-Process.txt',
		filesize: '43.5MB',
		date_modified: '06 Oct, 2021',
		avatar: '/images/avatar/avatar-1.jpg',
		uploadedby: 'Paul Haney',
		action: 1
	},
	{
		id: 27,
		filename: 'Geeks-UI-promo-trailer-template.mov',
		filesize: '115MB',
		date_modified: '08 Oct, 2021',
		avatar: '/images/avatar/avatar-2.jpg',
		uploadedby: '/images/avatar/avatar-1.jpg',
		action: 1
	},
	{
		id: 28,
		filename: 'Geeks-UI-Marketing-Process.txt',
		filesize: '43.5MB',
		date_modified: '06 Oct, 2021',
		avatar: '/images/avatar/avatar-3.jpg',
		uploadedby: 'Kristin Watson',
		action: 1
	},
	{
		id: 29,
		filename: 'Geeks-UI-Marketing-Process.ppt',
		filesize: '122MB',
		date_modified: '04 Oct, 2021',
		avatar: '/images/avatar/avatar-4.jpg',
		uploadedby: 'Nia Sikhone',
		action: 1
	},
	{
		id: 30,
		filename: 'Admin-Dashboard-Design.xlsx',
		filesize: '55MB',
		date_modified: '12 Oct, 2021',
		avatar: '/images/avatar/avatar-6.jpg',
		uploadedby: 'Kristin Watson',
		action: 1
	}
];

export const FilesData = [filesdata];
