import { v4 as uuid } from 'uuid';

export const ProjectsGridData = [
  {
    id: uuid(),
    title: "Franchiseen",
    category: "Service - Finance",
    projectbrief:
      "Quisque at augue convallis, tincidunt erat et, tristique ssa mollis dignissim eget",
    progress: "85",
    status: "In Progress",
    duedate: "25 Jan, 2022",
    budget: 2000,
    icon: "clipboard",
    coverimage: null,
    team: [1, 2, 3, 4, 5],
  },
  {
    id: uuid(),
    title: "Dietized",
    category: "Food - Health Kitchen",
    projectbrief:
      "Nam gravida vestibulum justo, ac aliquet erat. Pellentesque vitae massa lacus.",
    progress: "95",
    status: "Pending",
    duedate: "30 May, 2022",
    budget: 800,
    icon: "message-square",
    coverimage: null,
    team: [1, 2, 3, 4, 5, 6, 7],
  },
  {
    id: uuid(),
    title: "Chefless",
    category: "Food - Fast Food",
    projectbrief:
      "Donec vel tellus nec purus mollis consequat sed at urna. In sit amet vehicula odio.",
    progress: "100",
    status: "Finished",
    duedate: "31 June, 2022",
    budget: 253000,
    icon: "shopping-cart",
    coverimage: null,
    team: [10, 9, 3, 4, 5, 6, 7, 8],
  },
  {
    id: uuid(),
    title: "Cuestay",
    category: "Service - Real Estate",
    projectbrief:
      "Nam gravida vestibulum justo, ac aliquet erat. Pellentesque vitae massa lacus.",
    progress: "95",
    status: "Pending",
    duedate: "30 May, 2022",
    budget: 800,
    icon: "message-square",
    coverimage: null,
    team: [9, 1, 3, 4, 5, 6, 7],
  },
  {
    id: uuid(),
    title: "Codelude",
    category: "Food - Fast Food",
    projectbrief:
      "Donec vel tellus nec purus mollis consequat sed at urna. In sit amet vehicula odio.",
    progress: "100",
    status: "Finished",
    duedate: "31 June, 2022",
    budget: 253000,
    icon: "shopping-cart",
    coverimage: null,
    team: [10, 9, 3, 4, 5, 6, 7, 8],
  },
  {
    id: uuid(),
    title: "Mussles",
    category: "Service - Real Estate",
    projectbrief:
      "Nam gravida vestibulum justo, ac aliquet erat. Pellentesque vitae massa lacus.",
    progress: "95",
    status: "Pending",
    duedate: "30 May, 2022",
    budget: 800,
    icon: "message-square",
    coverimage: null,
    team: [9, 1, 3, 4, 5, 6, 7],
  },
];

export default ProjectsGridData;
