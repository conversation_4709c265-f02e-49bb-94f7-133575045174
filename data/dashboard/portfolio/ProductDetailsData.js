
export const ProductDetails = [
	{
		id: 1,
		title: 'Product Details',
		content: `<p>My name is <PERSON> and I work as human duct tape at Gatsby, 
        that means that I do a lot of different things. Everything from dev 
        roll to writing content to writing code. And I used to work as an architect at IBM. 
        I live in Portland, Oregon.</p>`
	},
	{
		id: 2,
		title: 'Specifications',
		content: `<p>We'll dive into GraphQL, the fundamentals of GraphQL. 
        We're only gonna use the pieces of it that we need to build in Gatsby. 
        We're not gonna be doing a deep dive into what GraphQL is or the language specifics. 
        We're also gonna get into MDX. MDX is a way to write React components in your markdown.</p>`
	},
	{
		id: 3,
		title: 'Free Shipping Policy',
		content: `<p>We'll dive into GraphQL, the fundamentals of GraphQL. 
        We're only gonna use the pieces of it that we need to build in Gatsby. 
        We're not gonna be doing a deep dive into what GraphQL is or the language specifics.
        We're also gonna get into MDX. MDX is a way to write React components in your markdown.</p>`
	},
	{
		id: 4,
		title: 'Refund Policy',
		content: `<p>
        We'll dive into GraphQL, the fundamentals of GraphQL. We're only gonna use the pieces of it that we need to build in Gatsby. We're not gonna be doing a deep dive into what GraphQL is or the language specifics. We're also gonna get into MDX. MDX is a way
        to write React components in your markdown.</p>
        <p>We'll dive into GraphQL, the fundamentals of GraphQL. We're only gonna use the pieces of it that we need to build in Gatsby. We're not gonna be doing a deep dive into what GraphQL is or the language specifics. We're also gonna get into MDX. MDX is a way
            to write React components in your markdown.</p>`
	}
];


export const Reviews = [
	{
		id: 1,
		customer: 'James Ennis',
		postedon: '28 Aug 2023',
		rating: 5,
		review: `<p>It's awesome , I never thought about geeks that awesome shoes.very pretty.</p>`
	},
	{
		id: 2,
		customer: 'Bradley Mouton',
		postedon: '21 Apr 2023',
		rating: 5,
		review: `<p>Quality is more than good that I was expected for buying. I first time purchase Geeks shoes & this brand is good. 
		Thanks to Geeks UI delivery was faster than fast ...Love Geeks UI</p>`
	},
	{
		id: 3,
		customer: 'Kieth J. Watson',
		postedon: '21 May 2023',
		rating: 4.5,
		review: `<p>Excellent shoes with original logo , Thanks Geeks UI , Buy these shoes without any tension.</p>`
	}
];

export const ProductDetailsData = [ProductDetails, Reviews];

export default ProductDetailsData;
