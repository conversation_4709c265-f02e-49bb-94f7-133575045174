export const AllCoursesData = [
	{
		id: 1,
		category: 'gatsby',
		image: '/images/course/course-gatsby.jpg',
		title: 'Revolutionize how you build the web',
		date_added: 'Added on 7 July, 2020',
		instructor_name: '<PERSON>',
		instructor_image: '/images/avatar/avatar-7.jpg',
		status: 'Pending',
		level: 'Intermediate',
		duration: '1h 46m',
		price: 850,
		discount: 50,
		rating: 2.0,
		ratingby: 16500,
		recommended: false,
		popular: false,
		trending: true,
		progress: 45
	},
	{
		id: 2,
		category: 'graphql',
		image: '/images/course/course-graphql.jpg',
		title: 'GraphQL: introduction to graphQL for beginners',
		date_added: 'Added on 6 July, 2021',
		instructor_name: '<PERSON>',
		instructor_image: '/images/avatar/avatar-6.jpg',
		status: 'Pending',
		level: 'Advance',
		duration: '2h 40m',
		price: 600,
		discount: 100,
		rating: 2.5,
		ratingby: 1500,
		recommended: true,
		popular: false,
		trending: false,
		progress: 95
	},
	{
		id: 3,
		category: 'html',
		image: '/images/course/course-html.jpg',
		title: 'HTML Full Course - Build a Website Tutorial',
		date_added: 'Added on 5 July, 2021',
		instructor_name: '<PERSON><PERSON> <PERSON>',
		instructor_image: '/images/avatar/avatar-5.jpg',
		status: 'Pending',
		level: 'Beginner',
		duration: '3h 16m',
		price: 700,
		discount: 150,
		rating: 3.0,
		ratingby: 1600,
		recommended: false,
		popular: true,
		trending: true,
		progress: 55
	},
	{
		id: 4,
		category: 'javascript',
		image: '/images/course/course-javascript.jpg',
		title: 'A Complete Beginner’s Guide to JavaScript',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Jenny Wilson',
		instructor_image: '/images/avatar/avatar-1.jpg',
		status: 'Live',
		level: 'Advance',
		duration: '4h 10m',
		price: 850,
		discount: 150,
		rating: 3.5,
		ratingby: 7500,
		recommended: true,
		popular: true,
		trending: false,
		progress: 50
	},
	{
		id: 5,
		category: 'nodejs',
		image: '/images/course/course-node.jpg',
		title: 'Beginning Node.js, Express & MongoDB Development',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		status: 'Live',
		level: 'Intermediate',
		duration: '2h 00m',
		price: 950,
		discount: 150,
		rating: 4.0,
		ratingby: 15700,
		recommended: true,
		popular: true,
		trending: true,
		progress: 45
	},
	{
		id: 6,
		category: 'laravel',
		image: '/images/course/course-laravel.jpg',
		title:
			'Laravel: The Ultimate Beginner’s Guide to Learn Laravel Step by Step',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Live',
		level: 'Beginner',
		duration: '1h 00m',
		price: 220,
		discount: 50,
		rating: 4.5,
		ratingby: 2500,
		recommended: true,
		popular: false,
		trending: true,
		progress: 65
	},
	{
		id: 7,
		category: 'react',
		image: '/images/course/course-react.jpg',
		title: 'How to easily create a website with React',
		date_added: 'Added on 4 July, 2021',
		instructor_name: 'April Noms',
		instructor_image: '/images/avatar/avatar-2.jpg',
		status: 'Live',
		level: 'Advance',
		duration: '3h 55m',
		price: 750,
		discount: 150,
		rating: 4.5,
		ratingby: 1500,
		recommended: true,
		popular: true,
		trending: true,
		progress: 75
	},
	{
		id: 8,
		category: 'angular',
		image: '/images/course/course-angular.jpg',
		title: 'Angular - the complete guide for beginner',
		date_added: 'Added on 3 July, 2021',
		instructor_name: 'Jacob Jones',
		instructor_image: '/images/avatar/avatar-4.jpg',
		status: 'Pending',
		level: 'Intermediate',
		duration: '2h 46m',
		price: 750,
		discount: 150,
		rating: 4.5,
		ratingby: 1600,
		recommended: true,
		popular: true,
		trending: true,
		progress: 45
	},
	{
		id: 9,
		category: 'laravel',
		image: '/images/course/course-laravel.jpg',
		title:
			"Laravel: The Ultimate Beginner's Guide to Learn Laravel Step by Step",
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Live',
		level: 'Beginner',
		duration: '2h 46m',
		price: 750,
		discount: 150,
		rating: 4.5,
		ratingby: 11500,
		recommended: true,
		popular: true,
		trending: true,
		progress: 59
	},
	{
		id: 10,
		category: 'nodejs',
		image: '/images/course/course-node.jpg',
		title: 'Beginning Node.js, Express & MongoDB Development',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		status: 'Live',
		level: 'Intermediate',
		duration: '2h 40m',
		price: 750,
		discount: 150,
		rating: 4.5,
		ratingby: 13500,
		recommended: true,
		popular: true,
		trending: true,
		progress: 95
	},
	{
		id: 11,
		category: 'python',
		image: '/images/course/course-python.jpg',
		title: 'The Python Course: build web application',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Ted Hawkins',
		instructor_image: '/images/avatar/avatar-10.jpg',
		status: 'Live',
		level: 'Beginner',
		duration: '2h 46m',
		price: 750,
		discount: 150,
		rating: 4.5,
		ratingby: 7800,
		recommended: true,
		popular: true,
		trending: true,
		progress: 45
	},
	{
		id: 12,
		category: 'laravel',
		image: '/images/course/course-laravel.jpg',
		title:
			'Laravel: The Ultimate Beginner’s Guide to Learn Laravel Step by Step',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Live',
		level: 'Beginner',
		duration: '1h 46m',
		price: 750,
		discount: 150,
		rating: 4.5,
		ratingby: 3600,
		recommended: true,
		popular: true,
		trending: true,
		progress: 95
	},
	{
		id: 13,
		category: 'react',
		image: '/images/course/course-react.jpg',
		title: 'How to easily create a website with React',
		date_added: 'Added on 4 July, 2021',
		instructor_name: 'April Noms',
		instructor_image: '/images/avatar/avatar-2.jpg',
		status: 'Live',
		level: 'Intermediate',
		duration: '2h 40m',
		price: 750,
		discount: 150,
		rating: 4.5,
		ratingby: 16500,
		recommended: true,
		popular: true,
		trending: true,
		progress: 65
	},
	{
		id: 14,
		category: 'angular',
		image: '/images/course/course-angular.jpg',
		title: 'Angular - the complete guide for beginner',
		date_added: 'Added on 3 July, 2021',
		instructor_name: 'Jacob Jones',
		instructor_image: '/images/avatar/avatar-4.jpg',
		status: 'Pending',
		level: 'Intermediate',
		duration: '6h 00m',
		price: 750,
		discount: 150,
		rating: 4.5,
		ratingby: 1500,
		recommended: true,
		popular: true,
		trending: true,
		progress: 85
	},
	{
		id: 15,
		category: 'gatsby',
		image: '/images/course/course-gatsby.jpg',
		title: 'Revolutionize how you build the web',
		date_added: 'Added on 7 July, 2020',
		instructor_name: 'Jenny Wilson',
		instructor_image: '/images/avatar/avatar-7.jpg',
		status: 'Pending',
		level: 'Advance',
		duration: '6h 46m',
		price: 750,
		discount: 150,
		rating: 3.0,
		ratingby: 1653,
		recommended: true,
		popular: true,
		trending: true,
		progress: 45
	},
	{
		id: 16,
		category: 'graphql',
		image: '/images/course/course-graphql.jpg',
		title: 'GraphQL: introduction to graphQL for beginners',
		date_added: 'Added on 6 July, 2021',
		instructor_name: 'Brooklyn Simmons',
		instructor_image: '/images/avatar/avatar-6.jpg',
		status: 'Pending',
		level: 'Beginner',
		duration: '5h 46m',
		price: 750,
		discount: 150,
		rating: 5.0,
		ratingby: 16500,
		recommended: true,
		popular: true,
		trending: true,
		progress: 55
	},
	{
		id: 17,
		category: 'angular',
		image: '/images/course/course-angular.jpg',
		title: 'Angular - the complete guide for beginner',
		date_added: 'Added on 3 July, 2021',
		instructor_name: 'Jenny Wilson',
		instructor_image: '/images/avatar/avatar-4.jpg',
		status: 'Pending',
		level: 'Intermediate',
		duration: '3h 30m',
		price: 750,
		discount: 150,
		rating: 4.5,
		ratingby: 1800,
		recommended: true,
		popular: true,
		trending: true,
		progress: 88
	},
	{
		id: 18,
		category: 'laravel',
		image: '/images/course/course-laravel.jpg',
		title:
			'Laravel: The Ultimate Beginner’s Guide to Learn Laravel Step by Step',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Live',
		level: 'Intermediate',
		duration: '2h 46m',
		price: 750,
		discount: 150,
		rating: 4.5,
		ratingby: 1200,
		recommended: true,
		popular: true,
		trending: true,
		progress: 45
	},
	{
		id: 19,
		category: 'nodejs',
		image: '/images/course/course-node.jpg',
		title: 'Beginning Node.js, Express & MongoDB Development',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		status: 'Live',
		level: 'Advance',
		duration: '2h 46m',
		price: 750,
		discount: 150,
		rating: 3.5,
		ratingby: 16800,
		recommended: true,
		popular: true,
		trending: true,
		progress: 55
	},
	{
		id: 20,
		category: 'javascript',
		image: '/images/course/course-javascript.jpg',
		title: 'Applying JavaScript and using the console.',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Guy Hawkins',
		instructor_image: '/images/avatar/avatar-10.jpg',
		status: 'Live',
		level: 'Beginner',
		duration: '2h 46m',
		price: 750,
		discount: 150,
		rating: 2.5,
		ratingby: 19500,
		recommended: true,
		popular: true,
		trending: true,
		progress: 95
	},
	{
		id: 21,
		category: 'javascript',
		image: '/images/course/course-javascript.jpg',
		title: 'Creating a Custom Event in Javascript',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Juanita Bell',
		instructor_image: '/images/avatar/avatar-10.jpg',
		status: 'Live',
		level: 'Beginner',
		duration: '2h 46m',
		price: 750,
		discount: 150,
		rating: 4.5,
		ratingby: 9300,
		recommended: false,
		popular: false,
		trending: false,
		progress: 55
	},
	{
		id: 22,
		category: 'javascript',
		image: '/images/course/course-javascript.jpg',
		title: 'Morden JavaScript Beginner Tutorial - Simple',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Ted Hawkins',
		instructor_image: '/images/avatar/avatar-10.jpg',
		status: 'Live',
		level: 'Beginner',
		duration: '2h 46m',
		price: 750,
		discount: 150,
		rating: 4.5,
		ratingby: 7800,
		recommended: false,
		popular: false,
		trending: false,
		progress: 45
	},
	{
		id: 23,
		category: 'css',
		image: '/images/course/course-css.jpg',
		title: 'CSS: ultimate CSS course from beginner to advanced',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Juanita Bell',
		instructor_image: '/images/avatar/avatar-7.jpg',
		status: 'Live',
		level: 'Beginner',
		duration: '1h 30m',
		price: 750,
		discount: 150,
		rating: 4.5,
		ratingby: 17000,
		recommended: true,
		popular: false,
		trending: false,
		progress: 95
	},
	{
		id: 24,
		category: 'css',
		image: '/images/course/course-wordpress.jpg',
		title: 'Wordpress: complete WordPress theme & plugin development',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Claire Robertson',
		instructor_image: '/images/avatar/avatar-8.jpg',
		status: 'Live',
		level: 'Beginner',
		duration: '1h 30m',
		price: 750,
		discount: 150,
		rating: 4.5,
		ratingby: 17000,
		recommended: true,
		popular: false,
		trending: false,
		progress: 95
	},
	{
		id: 25,
		category: 'javascript',
		image: '/images/course/course-javascript.jpg',
		title: 'JavaScript : A Complete Beginner’s Guide',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Guy Robertson',
		instructor_image: '/images/avatar/avatar-1.jpg',
		status: 'Live',
		level: 'Advance',
		duration: '4h 10m',
		price: 850,
		discount: 150,
		rating: 3.5,
		ratingby: 7500,
		recommended: false,
		popular: false,
		trending: false,
		progress: 95
	}
];

export default AllCoursesData;
