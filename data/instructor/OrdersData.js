export const OrdersData = [
	{
		id: 1,
		course: 'Building Scalable APIs with GraphQL',
		amount: 89,
		invoice: '10023',
		date: 'June 9, 2020',
		method: 'Amex'
	},
	{
		id: 2,
		course: 'HTML5 Web Front End Development',
		amount: 129,
		invoice: '10020',
		date: 'June 9, 2020',
		method: 'Mastercard'
	},
	{
		id: 3,
		course: 'Learn BasicJavaScript Courses',
		amount: 29,
		invoice: '10018',
		date: 'June 8, 2020',
		method: 'PayPal'
	},
	{
		id: 4,
		course: 'Get Started: React Js Courses',
		amount: 59,
		invoice: '10003',
		date: 'June 9, 2020',
		method: 'Visa'
	},
	{
		id: 5,
		course: 'Building Scalable APIs with GraphQL',
		amount: 129,
		invoice: '10020',
		date: 'June 7, 2020',
		method: 'Mastercard'
	},
	{
		id: 6,
		course: 'Master in CSS3 Courses Online',
		amount: 29,
		invoice: '10018',
		date: 'June 8, 2020',
		method: 'PayPal'
	},
	{
		id: 7,
		course: 'Online Angular Courses',
		amount: 59,
		invoice: '10003',
		date: 'June 6, 2020',
		method: 'Visa'
	},
	{
		id: 8,
		course: 'Master in CSS3 Courses Online',
		amount: 29,
		invoice: '10018',
		date: 'June 8, 2020',
		method: 'PayPal'
	},
	{
		id: 9,
		course: 'Online Angular Courses',
		amount: 59,
		invoice: '10003',
		date: 'June 6, 2020',
		method: 'Visa'
	},
	{
		id: 10,
		course: 'Online Angular Courses',
		amount: 89,
		invoice: '10010',
		date: 'June 5, 2020',
		method: 'Visa'
	},
	{
		id: 12,
		course: 'Master in CSS3 Courses Online',
		amount: 39,
		invoice: '10009',
		date: 'June 5, 2020',
		method: 'PayPal'
	},
	{
		id: 13,
		course: 'Online Angular Courses',
		amount: 89,
		invoice: '10008',
		date: 'June 5, 2020',
		method: 'Amex'
	},
	{
		id: 14,
		course: 'Building Scalable APIs with GraphQL',
		amount: 89,
		invoice: '10007',
		date: 'June 4, 2020',
		method: 'Amex'
	},
	{
		id: 15,
		course: 'Master in CSS3 Courses Online',
		amount: 79,
		invoice: '10006',
		date: 'June 4, 2020',
		method: 'Amex'
	},
	{
		id: 16,
		course: 'Building Scalable APIs with GraphQL',
		amount: 69,
		invoice: '10005',
		date: 'June 4, 2020',
		method: 'Amex'
	}
];

export default OrdersData;
