export const WithdrawHistoryData = [
	{
		id: 1060,
		status: 'Pending',
		amount: 1200,
		invoice: '10023',
		date: 'June 9, 2020',
		method: 'Amex'
	},
	{
		id: 1038,
		status: 'Paid',
		amount: 2000,
		invoice: '10020',
		date: 'June 9, 2020',
		method: 'Mastercard'
	},
	{
		id: 1016,
		status: 'Paid',
		amount: 3590,
		invoice: '10018',
		date: 'June 8, 2020',
		method: 'PayPal'
	},
	{
		id: 1008,
		status: 'Paid',
		amount: 4500,
		invoice: '10003',
		date: 'June 9, 2020',
		method: 'Visa'
	},
	{
		id: 1002,
		status: 'Paid',
		amount: 1232,
		invoice: '10020',
		date: 'June 7, 2020',
		method: 'Mastercard'
	},
	{
		id: 982,
		status: 'Cancel',
		amount: 4500,
		invoice: '10018',
		date: 'June 8, 2020',
		method: 'PayPal'
	},
	{
		id: 970,
		status: 'Paid',
		amount: 1232,
		invoice: '10003',
		date: 'June 6, 2020',
		method: 'Visa'
	},
	{
		id: 965,
		status: 'Paid',
		amount: 4235,
		invoice: '10018',
		date: 'June 8, 2020',
		method: 'PayPal'
	},
	{
		id: 953,
		status: 'Paid',
		amount: 1231,
		invoice: '10003',
		date: 'June 6, 2020',
		method: 'Visa'
	},
	{
		id: 943,
		status: 'Paid',
		amount: 5435,
		invoice: '10010',
		date: 'June 5, 2020',
		method: 'Visa'
	},
	{
		id: 933,
		status: 'Pending',
		amount: 2435,
		invoice: '10009',
		date: 'June 5, 2020',
		method: 'PayPal'
	},
	{
		id: 921,
		status: 'Cancel',
		amount: 3590,
		invoice: '10008',
		date: 'June 5, 2020',
		method: 'Amex'
	},
	{
		id: 853,
		status: 'Paid',
		amount: 1231,
		invoice: '10007',
		date: 'June 4, 2020',
		method: 'Amex'
	},
	{
		id: 844,
		status: 'Paid',
		amount: 4235,
		invoice: '10006',
		date: 'June 4, 2020',
		method: 'Amex'
	},
	{
		id: 816,
		status: 'Paid',
		amount: 5435,
		invoice: '10005',
		date: 'June 4, 2020',
		method: 'Amex'
	}
];

export default WithdrawHistoryData;
