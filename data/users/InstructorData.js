
export const InstructorData = [
	{
		id: 1,
		name: '<PERSON>',
		image: '/images/avatar/avatar-1.jpg',
		topic: 'Front-end Developer, Designer',
		courses: 6,
		joined: '7 July, 2020',
		students: 50274,
		rating: 4.6,
		reviews: 12230,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 2,
		name: '<PERSON><PERSON>',
		image: '/images/avatar/avatar-2.jpg',
		topic: 'Front End Developer',
		courses: 3,
		joined: '6 July, 2020',
		students: 26060,
		rating: 4.4,
		reviews: 11230,
		status: 'online',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 3,
		name: '<PERSON><PERSON>',
		image: '/images/avatar/avatar-17.jpg',
		topic: 'Web Developer, Designer, and Teacher',
		courses: 12,
		joined: '12 June, 2020',
		students: 8234,
		rating: 4.6,
		reviews: 10230,
		status: 'away',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 4,
		name: 'Jacob Jones',
		image: '/images/avatar/avatar-14.jpg',
		topic: 'Bootstrap Expert',
		courses: 2,
		joined: '2 July, 2020',
		students: 8234,
		rating: 4.5,
		reviews: 2230,
		status: 'info',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 5,
		name: 'Kristin Watson',
		image: '/images/avatar/avatar-7.jpg',
		topic: 'Web Development',
		courses: 1,
		joined: '1 July, 2020',
		students: 14944,
		rating: 4.5,
		reviews: 1230,
		status: 'busy',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 6,
		name: 'Nia Sikhone',
		image: '/images/avatar/avatar-17.jpg',
		topic: 'Web Developer, Designer, and Teacher',
		courses: 12,
		joined: '12 June, 2020',
		students: 6845,
		rating: 4.5,
		reviews: 12230,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 7,
		name: 'Rivao Luke',
		image: '/images/avatar/avatar-17.jpg',
		topic: 'Web Development',
		courses: 6,
		joined: '1 July, 2020',
		students: 8234,
		rating: 4.5,
		reviews: 12530,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 8,
		name: 'Nia Sikhone',
		image: '/images/avatar/avatar-4.jpg',
		topic: 'Web Developer, Designer, and Teacher',
		courses: 12,
		joined: '12 June, 2020',
		students: 6845,
		rating: 4.5,
		reviews: 12230,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 9,
		name: 'Xiaon Merry',
		image: '/images/avatar/avatar-6.jpg',
		topic: 'Web Developer, Designer, and Teacher',
		courses: 1,
		joined: '8 June, 2020',
		students: 8234,
		rating: 4.4,
		reviews: 12230,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 10,
		name: 'Sina Ray',
		image: '/images/avatar/avatar-3.jpg',
		topic: 'Engineering Architect',
		courses: 8,
		joined: '12 June, 2020',
		students: 3242,
		rating: 4.5,
		reviews: 12230,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 11,
		name: 'Kristin Watson',
		image: '/images/avatar/avatar-15.jpg',
		topic: 'Web Development',
		courses: 1,
		joined: '1 July, 2020',
		students: 14944,
		rating: 4.5,
		reviews: 14230,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 12,
		name: 'Jacob Jones',
		image: '/images/avatar/avatar-14.jpg',
		topic: 'Bootstrap Expert',
		courses: 2,
		joined: '2 July, 2020',
		students: 8234,
		rating: 4.5,
		reviews: 22230,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 13,
		name: 'Nia Sikhone',
		image: '/images/avatar/avatar-17.jpg',
		topic: 'Web Developer, Designer, and Teacher',
		courses: 12,
		joined: '12 June, 2020',
		students: 8234,
		rating: 4.5,
		reviews: 12530,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 14,
		name: 'Sina Ray',
		image: '/images/avatar/avatar-13.jpg',
		topic: 'Front End Developer',
		courses: 3,
		joined: '6 July, 2020',
		students: 26060,
		rating: 4.1,
		reviews: 14230,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 15,
		name: 'Reva Yokk',
		image: '/images/avatar/avatar-12.jpg',
		topic: 'Engineering Architect',
		courses: 6,
		joined: '7 July, 2020',
		students: 50274,
		rating: 4.5,
		reviews: 12230,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 16,
		name: 'Xiaon Merry',
		image: '/images/avatar/avatar-18.jpg',
		topic: 'Web Developer, Designer, and Teacher',
		courses: 1,
		joined: '8 June, 2020',
		students: 8234,
		rating: 4.4,
		reviews: 10230,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 17,
		name: 'Sina Ray',
		image: '/images/avatar/avatar-3.jpg',
		topic: 'Engineering Architect',
		courses: 8,
		joined: '12 June, 2020',
		students: 3242,
		rating: 4.5,
		reviews: 12230,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 18,
		name: 'Nia Sikhone',
		image: '/images/avatar/avatar-4.jpg',
		topic: 'Web Developer, Designer, and Teacher',
		courses: 12,
		joined: '12 June, 2020',
		students: 6845,
		rating: 4.5,
		reviews: 18230,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 19,
		name: 'Nia Sikhone',
		image: '/images/avatar/avatar-17.jpg',
		topic: 'Web Developer, Designer, and Teacher',
		courses: 12,
		joined: '12 June, 2020',
		students: 6845,
		rating: 4.5,
		reviews: 12000,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	},
	{
		id: 20,
		name: 'Rivao Luke',
		image: '/images/avatar/avatar-17.jpg',
		topic: 'Web Development',
		courses: 6,
		joined: '1 July, 2020',
		students: 8234,
		rating: 4.5,
		reviews: 19230,
		status: 'offline',
		about: `I am an Innovation designer focussing on UX/UI based in Berlin. 
        As a creative resident at Figma explored the city of the future and how new technologies like AI, 
        voice control, and augmented reality will change our interfaces.`
	}
];

export default InstructorData;
