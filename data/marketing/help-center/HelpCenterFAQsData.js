import { v4 as uuid } from 'uuid';

export const MostAskedFAQs = [
	{
		id: uuid(),
		title: 'Is there a 14-days trial?',
		content: `Lorem ipsum dolor, sit amet consectetur adipisicing elit. Fuga aperiam velit possimus! Voluptatum
			aliquid minima dolorum, enim perspiciatis sit. Distinctio quidem perferendis reprehenderit quisquam
			cum natus id, iusto, aut, quasi modi nihil veritatis! Possimus eum beatae dolor rem quidem libero sit
			odio quia eius ullam.`
	},
	{
		id: uuid(),
		title: 'What’s the benefits of the Premium Membership?',
		content: `Leggings occaecat craft beer farm-to-table, raw denim
		aesthetic synth
		nesciunt you probably haven't heard of them accusamus labore
		sustainable VHS.
		<br>
		<br>
		Lorem ipsum dolor sit amet consectetur adipisicing elit.
		Tenetur sit quos facere? Eum praesentium aut voluptatem,
		ullam, quia tempora quam non, beatae repudiandae esse
		excepturi sapiente tempore illo nobis fugiat nulla.
		Obcaecati voluptatum quos a vero minus dicta veniam illo
		cupiditate. Ipsam, fuga!`
	},
	{
		id: uuid(),
		title: 'How much time I will need to learn this app?',
		content: `Necessitatibus alias at labore explicabo totam illum saepe error delectus excepturi. Dolorem vitae
		eaque magnam quidem optio explicabo ipsa quae in nisi aut voluptates ratione cumque dignissimos non at
		voluptatum quis soluta, modi dolorum nesciunt porro praesentium corporis commodi doloremque.<br><br>
		Architecto eaque vitae quaerat quo in, voluptas earum hic repudiandae assumenda, incidunt, mollitia
		numquam illo saepe impedit!`
	},
	{
		id: uuid(),
		title: 'Are there any free tutorials available?',
		content: `Tenetur rem error, voluptatem fugit iusto laudantium laborum at molestias dolorum sint facilis
		voluptas alias soluta consequuntur. Id saepe officiis voluptates sit fuga repellat facilis tempore vel
		explicabo unde, dicta, natus, amet repellendus ea reprehenderit! Perspiciatis non modi beatae vel est.
		Nisi?`
	},
	{
		id: uuid(),
		title: 'Is there a month-to-month payment option?',
		content: `Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad
		squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck
		quinoa nesciunt laborum eiusmod.`
	},
	{
		id: uuid(),
		title: 'How do I take a Geeks course?',
		content: `Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad
		squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck
		quinoa nesciunt laborum eiusmod.`
	},
	{
		id: uuid(),
		title: 'How Does Geeks Courses Work?',
		content: `Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad
		squid. 3 wolf moon officia aute, non cupidatat skateboard dolor brunch. Food truck
		quinoa nesciunt laborum eiusmod.`
	}
];

export const GeneralInquiriesFAQs = [
	{
		id: uuid(),
		title: 'Is there a 14-days trial?',
		content: `Vestibulum nec porta tortor. Phasellus metus quam, semper ut
		tincidunt sit amet, viverra quis neque. Nullam
		mattis mollis massa nec pulvinar. In eu tellus quis urna
		vestibulum pulvinar.`
	},
	{
		id: uuid(),
		title: 'What’s the benefits of the Premium Membership?',
		content: `Leggings occaecat craft beer farm-to-table, raw denim aesthetic synth nesciunt you probably haven't heard of them accusamus labore sustainable VHS`
	},
	{
		id: uuid(),
		title: 'How much time I will need to learn this app?',
		content: `Anim pariatur cliche reprehenderit, enim eiusmod high life
		accusamus terry richardson ad
		squid. 3 wolf moon officia aute, non cupidatat skateboard
		dolor brunch. Food truck
		quinoa nesciunt laborum eiusmod.`
	},
	{
		id: uuid(),
		title: 'Are there any free tutorials available?',
		content: `Anim pariatur cliche reprehenderit, enim eiusmod high life
		accusamus terry richardson ad
		squid. 3 wolf moon officia aute, non cupidatat skateboard
		dolor brunch. Food truck
		quinoa nesciunt laborum eiusmod.`
	}
];

export const SupportFAQs = [
	{
		id: uuid(),
		title: 'Are there any free tutorials available?',
		content: `Vestibulum nec porta tortor. Phasellus metus quam, semper ut
		tincidunt sit amet, viverra quis neque. Nullam
		mattis mollis massa nec pulvinar. In eu tellus quis urna
		vestibulum pulvinar.`
	},
	{
		id: uuid(),
		title: 'Is there a month-to-month payment option?',
		content: `Leggings occaecat craft beer farm-to-table, raw denim
		aesthetic synth
		nesciunt you probably haven't heard of them accusamus labore
		sustainable VHS.`
	},
	{
		id: uuid(),
		title: 'How do I take a Geeks course?',
		content: `Anim pariatur cliche reprehenderit, enim eiusmod high life
		accusamus terry richardson ad
		squid. 3 wolf moon officia aute, non cupidatat skateboard
		dolor brunch. Food truck
		quinoa nesciunt laborum eiusmod.`
	},
	{
		id: uuid(),
		title: 'How Does Geeks Courses Work?',
		content: `Anim pariatur cliche reprehenderit, enim eiusmod high life
		accusamus terry richardson ad
		squid. 3 wolf moon officia aute, non cupidatat skateboard
		dolor brunch. Food truck
		quinoa nesciunt laborum eiusmod.`
	}
];

export const HelpCenterFAQsData = [
	MostAskedFAQs,
	GeneralInquiriesFAQs,
	SupportFAQs
];

export default HelpCenterFAQsData;
