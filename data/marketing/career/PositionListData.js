import { v4 as uuid } from 'uuid';

const PositionListData = [
  {
    id: uuid(),
    department: "Adminisration",
    description:
      "Put your creative ideas into action at a product-driven and design-led organization.",
    jobtitles: [
      {
        id: uuid(),
        designation: "Human Resource",
        location: "Dubai",
        link: "/career/human-resource",
        remote: false,
        startup: "Sharif",
      },
      {
        id: uuid(),
        designation: "Business Development",
        location: "Dubai",
        link: "/career/business-development",
        remote: true,
        startup: "Sharif",
      },
      {
        id: uuid(),
        designation: "Partner Success",
        location: "Dubai",
        link: "/career/partner-success",
        remote: true,
        startup: "Sharif",
      },
    ],
  },
  {
    id: uuid(),
    department: "Finance",
    description:
      "Put your creative ideas into action at a product-driven and design-led organization.",
    jobtitles: [
      {
        id: uuid(),
        designation: "Investor Relations",
        location: "Dubai",
        link: "/career/investor-relations",
        remote: false,
        startup: "Sharif",
      },
      {
        id: uuid(),
        designation: "Fundraising Analyist",
        location: "Dubai",
        link: "/career/fundraising-analyist",
        remote: true,
        startup: "<PERSON>",
      },
      {
        id: uuid(),
        designation: "Accountant",
        location: "Dubai",
        link: "/career/accountant",
        remote: true,
        startup: "Sharif",
      },
      // {
      //   id: uuid(),
      //   designation: "Bookeeping",
      //   location: "Dubai",
      //   link: "/career/bookeeping",
      //   remote: true,
      //   startup: "Sharif",
      // },
    ],
  },
  {
    id: uuid(),
    department: "Engineering",
    description:
      "Develop quality code that is built for scale for millions of people looking to eliminate “work about work” with our platform.",
    jobtitles: [
      {
        id: uuid(),
        designation: "ITIL Project Manager",
        location: "Bangalore (Remote)",
        link: "/career/itil-project-manager",
        remote: true,
        startup: "Codelude",
      },
      {
        id: uuid(),
        designation: "Product Manager",
        location: "Bangalore (Remote)",
        link: "/career/product-manager",
        remote: true,
        startup: "Codelude",
      },
      {
        id: uuid(),
        designation: "NextJS Developer",
        location: "Bangalore (Remote)",
        link: "/career/react-developer",
        remote: true,
        startup: "Codelude",
      },
      {
        id: uuid(),
        designation: "Flutter Developer",
        location: "Bangalore (Remote)",
        link: "/career/flutter-developer",
        remote: true,
        startup: "Codelude",
      },
      {
        id: uuid(),
        designation: "QA Tester",
        location: "Bangalore (Remote)",
        link: "/career/qa-tester",
        remote: true,
        startup: "Codelude",
      },
    ],
  },
  {
    id: uuid(),
    department: "Marketing",
    description:
      "Recruit, retain, and enable our growing number of Geeks through programs that support our people and our business.",
    jobtitles: [
      {
        id: uuid(),
        designation: "Campaign Manager",
        location: "Bangalore",
        link: "/career/campaign-manager",
        remote: true,
        startup: "Codelude",
      },
      {
        id: uuid(),
        designation: "Community Manager",
        location: "Bangalore",
        link: "/career/community-manager",
        remote: true,
        startup: "Codelude",
      },
      {
        id: uuid(),
        designation: "Digital Marketing Specialist",
        location: "Bangalore",
        link: "/career/digital-marketing",
        remote: true,
        startup: "Codelude",
      },
      {
        id: uuid(),
        designation: "Graphic Designer",
        location: "Bangalore",
        link: "/career/graphic-designer",
        remote: true,
        startup: "Codelude",
      },
    ],
  },
  {
    id: uuid(),
    department: "Sales",
    description:
      "Recruit, retain, and enable our growing number of Geeks through programs that support our people and our business.",
    jobtitles: [
      {
        id: uuid(),
        designation: "Account Executive",
        location: "Bangalore",
        link: "/career/account-executive",
        remote: false,
      },
      {
        id: uuid(),
        designation: "Customer Success Manager",
        location: "Bangalore",
        link: "/career/customer-success-manager",
        remote: false,
      },
      {
        id: uuid(),
        designation: "Sales Representative",
        location: "Bangalore",
        link: "/career/sales-representative",
        remote: false,
      },
    ],
  },
  {
    id: uuid(),
    department: "Operations",
    description:
      "Put your creative ideas into action at a product-driven and design-led organization.",
    jobtitles: [
      {
        id: uuid(),
        designation: "Production Manager",
        location: "Bangalore",
        link: "/career/production-manager",
        remote: false,
      },
      {
        id: uuid(),
        designation: "Procurement Manager",
        location: "Bangalore",
        link: "/career/precurement-manager",
        remote: false,
      },
      {
        id: uuid(),
        designation: "Supply Chain Manager",
        location: "Bangalore",
        link: "/career/supply-chain",
        remote: false,
      },
      {
        id: uuid(),
        designation: "Logistic Manager",
        location: "Bangalore",
        link: "/career/logic-executive",
        remote: false,
      },
    ],
  },
];

export default PositionListData;
