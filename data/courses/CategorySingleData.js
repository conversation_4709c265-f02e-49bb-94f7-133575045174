export const allcourses = [
	{
		id: 1,
		image: '/images/course/course-gatsby.jpg',
		title: 'Revolutionize how you build the web',
		date_added: 'Added on 7 July, 2020',
		instructor_name: '<PERSON><PERSON>',
		instructor_image: '/images/avatar/avatar-7.jpg',
		enrolled: 12234
	},
	{
		id: 2,
		image: '/images/course/course-graphql.jpg',
		title: 'Guide to Static Sites with Gatsby',
		date_added: 'Added on 6 July, 2021',
		instructor_name: '<PERSON>',
		instructor_image: '/images/avatar/avatar-6.jpg',
		enrolled: 2000
	},
	{
		id: 3,
		image: '/images/course/course-html.jpg',
		title: 'The Modern HTML Courses',
		date_added: 'Added on 5 July, 2021',
		instructor_name: '<PERSON><PERSON>',
		instructor_image: '/images/avatar/avatar-5.jpg',
		enrolled: 22345
	},
	{
		id: 4,
		image: '/images/course/course-javascript.jpg',
		title: 'Courses JavaScript Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: '<PERSON>',
		instructor_image: '/images/avatar/avatar-1.jpg',
		enrolled: 5235
	},
	{
		id: 5,
		image: '/images/course/course-node.jpg',
		title: 'Get Start with Node Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		enrolled: 7200
	},
	{
		id: 6,
		image: '/images/course/course-laravel.jpg',
		title: 'Get Start with Laravel',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		enrolled: 22500
	},
	{
		id: 7,
		image: '/images/course/course-react.jpg',
		title: 'Get Start with React',
		date_added: 'Added on 4 July, 2021',
		instructor_name: 'April Noms',
		instructor_image: '/images/avatar/avatar-2.jpg',
		enrolled: 6759
	},
	{
		id: 8,
		image: '/images/course/course-angular.jpg',
		title: 'Get Start with Angular',
		date_added: 'Added on 3 July, 2021',
		instructor_name: 'Jacob Jones',
		instructor_image: '/images/avatar/avatar-4.jpg',
		enrolled: 7234
	},
	{
		id: 9,
		image: '/images/course/course-laravel.jpg',
		title: 'Get Start with Laravel',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		enrolled: 6759
	},
	{
		id: 10,
		image: '/images/course/course-node.jpg',
		title: 'Get Start with Node Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		enrolled: 22500
	},
	{
		id: 11,
		image: '/images/course/course-javascript.jpg',
		title: 'Courses JavaScript Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Guy Hawkins',
		instructor_image: '/images/avatar/avatar-10.jpg',
		enrolled: 2000
	},
	{
		id: 12,
		image: '/images/course/course-laravel.jpg',
		title: 'Get Start with Laravel',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		enrolled: 22500
	},
	{
		id: 13,
		image: '/images/course/course-react.jpg',
		title: 'Get Start with React',
		date_added: 'Added on 4 July, 2021',
		instructor_name: 'April Noms',
		instructor_image: '/images/avatar/avatar-2.jpg',
		enrolled: 6759
	},
	{
		id: 14,
		image: '/images/course/course-angular.jpg',
		title: 'Get Start with Angular',
		date_added: 'Added on 3 July, 2021',
		instructor_name: 'Jacob Jones',
		instructor_image: '/images/avatar/avatar-4.jpg',
		enrolled: 5235
	},
	{
		id: 15,
		image: '/images/course/course-gatsby.jpg',
		title: 'Revolutionize how you build the web',
		date_added: 'Added on 7 July, 2020',
		instructor_name: 'Reva Yokk',
		instructor_image: '/images/avatar/avatar-7.jpg',
		enrolled: 2000
	},
	{
		id: 16,
		image: '/images/course/course-graphql.jpg',
		title: 'Guide to Static Sites with Gatsby',
		date_added: 'Added on 6 July, 2021',
		instructor_name: 'Brooklyn Simmons',
		instructor_image: '/images/avatar/avatar-6.jpg',
		enrolled: 22500
	},
	{
		id: 17,
		image: '/images/course/course-angular.jpg',
		title: 'Get Start with Angular',
		date_added: 'Added on 3 July, 2021',
		instructor_name: 'Jacob Jones',
		instructor_image: '/images/avatar/avatar-4.jpg',
		enrolled: 6759
	},
	{
		id: 18,
		image: '/images/course/course-laravel.jpg',
		title: 'Get Start with Laravel',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		enrolled: 5235
	},
	{
		id: 19,
		image: '/images/course/course-node.jpg',
		title: 'Get Start with Node Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		enrolled: 2000
	},
	{
		id: 20,
		image: '/images/course/course-javascript.jpg',
		title: 'Courses JavaScript Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Guy Hawkins',
		instructor_image: '/images/avatar/avatar-10.jpg',
		enrolled: 22500
	}
];

export const CategorySingleData = [allcourses];
