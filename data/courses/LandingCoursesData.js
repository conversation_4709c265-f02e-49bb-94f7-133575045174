export const InstructorsList = [
  {
    id: 1,
    image: "/images/how/how-1.png",
    name: "Submit a Lead",
    designation:
      "Choose the startup(s) you're interested in and submit your investment inquiry through our platform, specifying the total investment amount.",
    link: "#",
  },
  {
    id: 2,
    image: "/images/how/how-2.png",
    name: "Initial Contact",
    step: 2,
    students: 5128,
    designation:
      "Our team will follow up via email to arrange an introductory meeting to discuss your investment interests and goals.",
    link: "#",
  },
  {
    id: 3,
    image: "/images/how/how-3.png",
    name: "Investment Opportunities",
    step: 8,
    students: 7423,
    designation:
      "In the meeting, we’ll present the startup’s investment opportunities, including share allotments, growth prospects, and potential returns.",
    link: "#",
    rating: 4.5,
  },
  {
    id: 4,
    image: "/images/how/how-4.png",
    name: "Investment Proposal",
    step: 10,
    students: 3896,
    designation:
      "After the meeting, we’ll provide you with a detailed Investment Proposal outlining the terms and conditions.",
    link: "#",
    rating: 4.5,
  },
  {
    id: 5,
    image: "/images/how/how-5.png",
    name: "Share Subscription Agreement (SSA)",
    step: 4,
    students: 5128,
    designation:
      "After review and signing, the investment is finalized, and shares are allotted.",
    link: "#",
    rating: 4.5,
  },
  {
    id: 6,
    image: "/images/how/how-6.png",
    name: "Post-Investment Monitoring",
    step: 8,
    students: 7423,
    designation:
      "After the investment, you can track the performance of your portfolio via the platform.",
    link: "#",
    rating: 4.5,
  },
];

export const TestimonialsList = [
	{
		id: 1,
		name: 'Barry Watson',
		designation: 'Web Developer,UK',
		image: '/images/avatar/avatar-1.jpg',
		content:
			'I started at stage zero. With Geeks I was able to start learning online and eventually build up enough knowledge and skills to transition into a well-paying career.',
		rating: 5.0
	},
	{
		id: 2,
		name: 'Linda Shenoy',
		designation: 'Developer and Bootcamp Instructor',
		image: '/images/avatar/avatar-2.jpg',
		content:
			'Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Etiam vulputate euismod justo in consequat. Sed tempus elementum urnanisl et lacus.',
		rating: 5.0
	},
	{
		id: 3,
		name: 'Jean Watson',
		designation: 'Engineering Architect',
		image: '/images/avatar/avatar-3.jpg',
		content:
			'Sed pretium risus magna, ac efficitur nunc rutrum imperdiet. Vivamus sed ante sed mi fermentum tempus. Nullam finibus augue eget felis efficitur semper.',
		rating: 5.0
	},

	{
		id: 4,
		name: 'John Deo',
		designation: 'Web Developer,UK',
		image: '/images/avatar/avatar-4.jpg',
		content:
			'Morbi quis posuere lacus. Morbi et metus sit amet tellus dapibus aliquam. Morbi consectetur magna vel turpis lobortis lorem iopsum dolor sit commodo.',
		rating: 4.5
	},
	{
		id: 5,
		name: 'Rubik Nanda',
		designation: 'Web Developer,UK',
		image: '/images/avatar/avatar-5.jpg',
		content:
			'Curabitur sollicitudin mi et sagittis egestas. Curabitur pellentesque nibh id enim hendrerit, at mollis neque rutrum. Sed nibh velit, tristique et dolor vitae.',
		rating: 4.5
	},
	{
		id: 6,
		name: 'Barry Watson',
		designation: 'Web Developer,UK',
		image: '/images/avatar/avatar-6.jpg',
		content:
			'Vestibulum in lobortis purus. Quisque sem turpis, hendrerit quis lacinia nec, rutrum nec velit. Nullam lobortis rhoncus tincidunt lorem ispun dnascetur ridiculus mus.',
		rating: 4.5
	},
	{
		id: 7,
		name: 'Jean Watson',
		designation: 'Web Developer,UK',
		image: '/images/avatar/avatar-7.jpg',
		content:
			'Praesent sit amet ornare magna, vitae consequat arcu. Vestibulum at dictum erat, a fringilla ante. Nam et nibh ut nunc rutrum suscipit quis non neque. Nulla facilisi.',
		rating: 4.5
	},
	{
		id: 8,
		name: 'Barry Watson',
		designation: 'Engineering Architect',
		image: '/images/avatar/avatar-8.jpg',
		content:
			'Sed pretium risus magna, ac efficitur nunc rutrum imperdiet. Vivamus sed ante sed mi fermentum tempus. Nullam finibus augue eget felis efficitur semper.',
		rating: 4.5
	}
];
export const LandingCoursesData = [InstructorsList, TestimonialsList];

export default LandingCoursesData;
