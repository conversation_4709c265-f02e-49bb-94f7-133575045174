export const allcourses = [
	{
		id: 1,
		image: '/images/course/course-gatsby.jpg',
		title: 'Revolutionize how you build the web',
		date_added: 'Added on 7 July, 2020',
		instructor_name: '<PERSON>',
		instructor_image: '/images/avatar/avatar-7.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 2,
		image: '/images/course/course-graphql.jpg',
		title: 'Guide to Static Sites with Gatsby',
		date_added: 'Added on 6 July, 2021',
		instructor_name: '<PERSON>',
		instructor_image: '/images/avatar/avatar-6.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 3,
		image: '/images/course/course-html.jpg',
		title: 'The Modern HTML Courses',
		date_added: 'Added on 5 July, 2021',
		instructor_name: '<PERSON><PERSON>',
		instructor_image: '/images/avatar/avatar-5.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 4,
		image: '/images/course/course-javascript.jpg',
		title: 'Courses JavaScript Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: '<PERSON>',
		instructor_image: '/images/avatar/avatar-1.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 5,
		image: '/images/course/course-node.jpg',
		title: 'Get Start with Node Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 6,
		image: '/images/course/course-laravel.jpg',
		title: 'Get Start with Laravel',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 7,
		image: '/images/course/course-react.jpg',
		title: 'Get Start with React',
		date_added: 'Added on 4 July, 2021',
		instructor_name: 'April Noms',
		instructor_image: '/images/avatar/avatar-2.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 8,
		image: '/images/course/course-angular.jpg',
		title: 'Get Start with Angular',
		date_added: 'Added on 3 July, 2021',
		instructor_name: 'Jacob Jones',
		instructor_image: '/images/avatar/avatar-4.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 9,
		image: '/images/course/course-laravel.jpg',
		title: 'Get Start with Laravel',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 10,
		image: '/images/course/course-node.jpg',
		title: 'Get Start with Node Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 11,
		image: '/images/course/course-javascript.jpg',
		title: 'Courses JavaScript Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Guy Hawkins',
		instructor_image: '/images/avatar/avatar-10.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 12,
		image: '/images/course/course-laravel.jpg',
		title: 'Get Start with Laravel',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 13,
		image: '/images/course/course-react.jpg',
		title: 'Get Start with React',
		date_added: 'Added on 4 July, 2021',
		instructor_name: 'April Noms',
		instructor_image: '/images/avatar/avatar-2.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 14,
		image: '/images/course/course-angular.jpg',
		title: 'Get Start with Angular',
		date_added: 'Added on 3 July, 2021',
		instructor_name: 'Jacob Jones',
		instructor_image: '/images/avatar/avatar-4.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 15,
		image: '/images/course/course-gatsby.jpg',
		title: 'Revolutionize how you build the web',
		date_added: 'Added on 7 July, 2020',
		instructor_name: 'Jenny Wilson',
		instructor_image: '/images/avatar/avatar-7.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 16,
		image: '/images/course/course-graphql.jpg',
		title: 'Guide to Static Sites with Gatsby',
		date_added: 'Added on 6 July, 2021',
		instructor_name: 'Brooklyn Simmons',
		instructor_image: '/images/avatar/avatar-6.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 17,
		image: '/images/course/course-angular.jpg',
		title: 'Get Start with Angular',
		date_added: 'Added on 3 July, 2021',
		instructor_name: 'Jacob Jones',
		instructor_image: '/images/avatar/avatar-4.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 18,
		image: '/images/course/course-laravel.jpg',
		title: 'Get Start with Laravel',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 19,
		image: '/images/course/course-node.jpg',
		title: 'Get Start with Node Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 20,
		image: '/images/course/course-javascript.jpg',
		title: 'Courses JavaScript Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Guy Hawkins',
		instructor_image: '/images/avatar/avatar-10.jpg',
		status: 'Live',
		action: 1
	}
];

export const allapprovedcourses = [
	{
		id: 4,
		image: '/images/course/course-javascript.jpg',
		title: 'Courses JavaScript Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Guy Hawkins',
		instructor_image: '/images/avatar/avatar-1.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 5,
		image: '/images/course/course-node.jpg',
		title: 'Get Start with Node Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 6,
		image: '/images/course/course-laravel.jpg',
		title: 'Get Start with Laravel',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 7,
		image: '/images/course/course-react.jpg',
		title: 'Get Start with React',
		date_added: 'Added on 4 July, 2021',
		instructor_name: 'April Noms',
		instructor_image: '/images/avatar/avatar-2.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 9,
		image: '/images/course/course-laravel.jpg',
		title: 'Get Start with Laravel',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 10,
		image: '/images/course/course-node.jpg',
		title: 'Get Start with Node Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 11,
		image: '/images/course/course-javascript.jpg',
		title: 'Courses JavaScript Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Guy Hawkins',
		instructor_image: '/images/avatar/avatar-10.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 12,
		image: '/images/course/course-laravel.jpg',
		title: 'Get Start with Laravel',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 13,
		image: '/images/course/course-react.jpg',
		title: 'Get Start with React',
		date_added: 'Added on 4 July, 2021',
		instructor_name: 'April Noms',
		instructor_image: '/images/avatar/avatar-2.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 18,
		image: '/images/course/course-laravel.jpg',
		title: 'Get Start with Laravel',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 19,
		image: '/images/course/course-node.jpg',
		title: 'Get Start with Node Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		status: 'Live',
		action: 1
	},
	{
		id: 20,
		image: '/images/course/course-javascript.jpg',
		title: 'Courses JavaScript Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Guy Hawkins',
		instructor_image: '/images/avatar/avatar-10.jpg',
		status: 'Live',
		action: 1
	}
];

export const allpendingcourses = [
	{
		id: 1,
		image: '/images/course/course-gatsby.jpg',
		title: 'Revolutionize how you build the web',
		date_added: 'Added on 7 July, 2020',
		instructor_name: 'Jenny Wilson',
		instructor_image: '/images/avatar/avatar-7.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 2,
		image: '/images/course/course-graphql.jpg',
		title: 'Guide to Static Sites with Gatsby',
		date_added: 'Added on 6 July, 2021',
		instructor_name: 'Brooklyn Simmons',
		instructor_image: '/images/avatar/avatar-6.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 3,
		image: '/images/course/course-html.jpg',
		title: 'The Modern HTML Courses',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Miston Wilson',
		instructor_image: '/images/avatar/avatar-5.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 8,
		image: '/images/course/course-angular.jpg',
		title: 'Get Start with Angular',
		date_added: 'Added on 3 July, 2021',
		instructor_name: 'Jacob Jones',
		instructor_image: '/images/avatar/avatar-4.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 14,
		image: '/images/course/course-angular.jpg',
		title: 'Get Start with Angular',
		date_added: 'Added on 3 July, 2021',
		instructor_name: 'Jacob Jones',
		instructor_image: '/images/avatar/avatar-4.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 15,
		image: '/images/course/course-gatsby.jpg',
		title: 'Revolutionize how you build the web',
		date_added: 'Added on 7 July, 2020',
		instructor_name: 'Jenny Wilson',
		instructor_image: '/images/avatar/avatar-7.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 16,
		image: '/images/course/course-graphql.jpg',
		title: 'Guide to Static Sites with Gatsby',
		date_added: 'Added on 6 July, 2021',
		instructor_name: 'Brooklyn Simmons',
		instructor_image: '/images/avatar/avatar-6.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 17,
		image: '/images/course/course-angular.jpg',
		title: 'Get Start with Angular',
		date_added: 'Added on 3 July, 2021',
		instructor_name: 'Jacob Jones',
		instructor_image: '/images/avatar/avatar-4.jpg',
		status: 'Pending',
		action: 2
	},
	{
		id: 18,
		image: '/images/course/course-laravel.jpg',
		title: 'Get Start with Laravel',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Pending',
		action: 1
	},
	{
		id: 19,
		image: '/images/course/course-node.jpg',
		title: 'Get Start with Node Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		status: 'Pending',
		action: 1
	},
	{
		id: 20,
		image: '/images/course/course-javascript.jpg',
		title: 'Courses JavaScript Heading Title',
		date_added: 'Added on 5 July, 2021',
		instructor_name: 'Guy Hawkins',
		instructor_image: '/images/avatar/avatar-10.jpg',
		status: 'Pending',
		action: 1
	}
];
export const AllCoursesData = [
	allcourses,
	allapprovedcourses,
	allpendingcourses
];

export default AllCoursesData;
