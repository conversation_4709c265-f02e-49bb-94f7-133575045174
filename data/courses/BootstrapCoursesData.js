export const BootstrapCoursesData = [
	{
		id: 1,
		category: 'bootstrap',
		image: '/images/course/course-bootstrap.jpg',
		title: 'An Ultimate Guide for Beginners Bootstrap 5',
		instructor_name: '<PERSON>',
		instructor_image: '/images/avatar/avatar-3.jpg',
		level: 'Beginner',
		duration: '1h 46m',
		rating: 4.0,
		ratingby: 9300
	},
	{
		id: 2,
		category: 'bootstrap',
		image: '/images/course/course-bootstrap.jpg',
		title: 'How to customize Bootstrap CSS for Beginner',
		instructor_name: '<PERSON>',
		instructor_image: '/images/avatar/avatar-2.jpg',
		level: 'Beginner',
		duration: '1h 46m',
		rating: 4.0,
		ratingby: 7800
	},
	{
		id: 3,
		category: 'bootstrap',
		image: '/images/course/course-bootstrap.jpg',
		title: 'Bootstrap 5 Grid System Introduction for Beginner',
		instructor_name: '<PERSON><PERSON>',
		instructor_image: '/images/avatar/avatar-4.jpg',
		level: 'Beginner',
		duration: '1h 46m',
		rating: 4.0,
		ratingby: 8245
	},
	{
		id: 4,
		category: 'bootstrap',
		image: '/images/course/course-bootstrap.jpg',
		title: 'Easy Way to Work with Bootstrap 5 Components',
		instructor_name: '<PERSON>',
		instructor_image: '/images/avatar/avatar-5.jpg',
		level: 'Beginner',
		duration: '1h 46m',
		rating: 4.0,
		ratingby: 3245
	},

	{
		id: 5,
		category: 'bootstrap',
		image: '/images/course/course-bootstrap.jpg',
		title: 'Bootstrap Tutorial - How to Set Up and Use Bootstrap',
		instructor_name: 'Morris Mccoy',
		instructor_image: '/images/avatar/avatar-5.jpg',
		level: 'Intermediate',
		duration: '3h 56m',
		rating: 4.0,
		ratingby: 5300
	},
	{
		id: 6,
		category: 'bootstrap',
		image: '/images/course/course-bootstrap.jpg',
		title: 'Bootstrap 5 Components Alerts Customization',
		instructor_name: 'Ted Hawkins',
		instructor_image: '/images/avatar/avatar-6.jpg',
		level: 'Intermediate',
		duration: '2h 46m',
		rating: 4.0,
		ratingby: 9300
	},
	{
		id: 7,
		category: 'bootstrap',
		image: '/images/course/course-bootstrap.jpg',
		title: 'Customization Bootstrap 5 Components Accordion /Tab',
		instructor_name: 'Juanita Bell',
		instructor_image: '/images/avatar/avatar-7.jpg',
		level: 'Intermediate',
		duration: '1h 30m',
		rating: 4.0,
		ratingby: 5568
	},
	{
		id: 8,
		category: 'bootstrap',
		image: '/images/course/course-bootstrap.jpg',
		title: 'Preparing a site to go live with Gatsby Server',
		instructor_name: 'Claire Robertson',
		instructor_image: '/images/avatar/avatar-8.jpg',
		level: 'Intermediate',
		duration: '2h 30m',
		rating: 4.0,
		ratingby: 6245
	},
	{
		id: 9,
		category: 'bootstrap',
		image: '/images/course/course-bootstrap.jpg',
		title: 'How to Build Custom Bootstrap Theme',
		instructor_name: 'Claire Robertson',
		instructor_image: '/images/avatar/avatar-8.jpg',
		level: 'Advance',
		duration: '2h 30m',
		rating: 4.0,
		ratingby: 6245
	},
	{
		id: 10,
		category: 'bootstrap',
		image: '/images/course/course-bootstrap.jpg',
		title: 'Bootstrap 5 tutorial - Learn Bootstrap by Building a page',
		instructor_name: 'Juanita Bell',
		instructor_image: '/images/avatar/avatar-7.jpg',
		level: 'Advance',
		duration: '1h 30m',
		rating: 4.0,
		ratingby: 5568
	},
	{
		id: 11,
		category: 'bootstrap',
		image: '/images/course/course-bootstrap.jpg',
		title: 'How to Build Admin Dashboard Bootstrap 5',
		instructor_name: 'Ted Hawkins',
		instructor_image: '/images/avatar/avatar-6.jpg',
		level: 'Advance',
		duration: '2h 46m',
		rating: 4.0,
		ratingby: 9300
	},
	{
		id: 12,
		category: 'bootstrap',
		image: '/images/course/course-bootstrap.jpg',
		title: 'Advance Bootstrap SCSS Theme Customization',
		instructor_name: 'Morris Mccoy',
		instructor_image: '/images/avatar/avatar-5.jpg',
		level: 'Advance',
		duration: '3h 56m',
		rating: 4.0,
		ratingby: 5300
	}
];

export default BootstrapCoursesData;
