export const MyCoursesData = [
	{
		id: 1,
		category: 'Courses',
		image: '/images/course/course-graphql.jpg',
		title: 'Revolutionize how you build the web',
		duration: '1h 30m',
		date: '7 July, 2021 1:42pm',
		instructor_name: '<PERSON><PERSON>',
		instructor_image: '/images/avatar/avatar-7.jpg',
		status: 'Draft',
		level: 'Beginner',
		students: 12234,
		rating: 4.5,
		votes: 3250
	},
	{
		id: 2,
		category: 'Marketing',
		image: '/images/course/course-gatsby.jpg',
		duration: '3h 40m',
		title: 'Guide to Static Sites with Gatsby',
		date: '6 July, 2021 2:42pm',
		instructor_name: '<PERSON>',
		instructor_image: '/images/avatar/avatar-6.jpg',
		status: 'Draft',
		level: 'Intermediate',
		students: 2000,
		rating: 4.5,
		votes: 5300,
		progress: 40
	},
	{
		id: 3,
		category: 'Workshop',
		image: '/images/course/course-html.jpg',
		duration: '4h 10m',
		title: 'The Modern HTML Courses',
		date: '5 July, 2021 5:42pm',
		instructor_name: '<PERSON><PERSON>',
		instructor_image: '/images/avatar/avatar-5.jpg',
		status: 'Pending',
		level: 'Beginner',
		students: 22345,
		rating: 4.5,
		votes: 6380
	},
	{
		id: 4,
		category: 'Company',
		image: '/images/course/course-javascript.jpg',
		duration: '4h 10m',
		title: 'Courses JavaScript Heading Title',
		date: '5 July, 2021 5:42pm',
		instructor_name: 'Guy Hawkins',
		instructor_image: '/images/avatar/avatar-1.jpg',
		status: 'Pending',
		level: 'Advance',
		students: 5235,
		rating: 4.5,
		votes: 5400,
		progress: 60
	},
	{
		id: 5,
		category: 'Workshop',
		image: '/images/course/course-node.jpg',
		duration: '2h 59m',
		title: 'Get Start with Node Heading Title',
		date: '5 July, 2021 5:42pm',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		status: 'Live',
		level: 'Intermediate',
		students: 7200,
		rating: 4.5,
		votes: 7800
	},
	{
		id: 6,
		category: 'Marketing',
		image: '/images/course/course-laravel.jpg',
		duration: '3h 40m',
		title: 'Get Start with Laravel',
		date: '5 July, 2021 5:42pm',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Live',
		level: 'Beginner',
		students: 22500,
		rating: 4.5,
		votes: 9200
	},
	{
		id: 7,
		category: 'Courses',
		image: '/images/course/course-react.jpg',
		duration: '4h 10m',
		title: 'Get Start with React',
		date: '4 July, 2021 12:42pm',
		instructor_name: 'April Noms',
		instructor_image: '/images/avatar/avatar-2.jpg',
		status: 'Pending',
		level: 'Beginner',
		students: 6759,
		rating: 4.5,
		votes: 3250
	},
	{
		id: 8,
		category: 'Workshop',
		image: '/images/course/course-angular.jpg',
		duration: '2h 59m',
		title: 'Get Start with Angular',
		date: '3 July, 2021 10:42am',
		instructor_name: 'Jacob Jones',
		instructor_image: '/images/avatar/avatar-4.jpg',
		status: 'Deleted',
		level: 'Advance',
		students: 7234,
		rating: 4.5,
		votes: 5400
	},
	{
		id: 9,
		category: 'Marketing',
		image: '/images/course/course-laravel.jpg',
		duration: '3h 40m',
		title: 'Get Start with Laravel',
		date: '5 July, 2021 5:42pm',
		instructor_name: 'Sobo Rikhan',
		instructor_image: '/images/avatar/avatar-9.jpg',
		status: 'Deleted',
		level: 'Beginner',
		students: 6759,
		rating: 4.5,
		votes: 7800
	},
	{
		id: 10,
		category: 'Workshop2',
		image: '/images/course/course-node.jpg',
		duration: '4h 40m',
		title: 'Get Start with Node Heading Title',
		date: '5 July, 2021 5:42pm',
		instructor_name: 'Sina Ray',
		instructor_image: '/images/avatar/avatar-3.jpg',
		status: 'Deleted',
		level: 'Intermediate',
		students: 22500,
		rating: 4.5,
		votes: 3250
	}
];

export default MyCoursesData;
