// import node module libraries
import { Fragment, useState } from 'react';
import Link from 'next/link';
import { Image, Navbar, Nav, Container } from 'react-bootstrap';

// import custom components
import NavDropdownMain from 'layouts/marketing/navbars/NavDropdownMain';

// import data files
import JobListingRoutes from 'routes/marketing/JobListingRoutes';

const NavbarJobPages = () => {
	
	const [expandedMenu, setExpandedMenu] = useState(false);
	return (
    <Fragment>
      <Navbar
        onToggle={(collapsed) => setExpandedMenu(collapsed)}
        expanded={expandedMenu}
        expand="lg"
        fixed="top"
        className="navbar-default shadow-none">
        <Container className="px-0">
          <Navbar.Brand as={Link} href="/">
            <Image src="/images/brand/logo/logo.svg" alt="" />
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="navbar-default">
            <span className="icon-bar top-bar mt-0"></span>
            <span className="icon-bar middle-bar"></span>
            <span className="icon-bar bottom-bar"></span>
          </Navbar.Toggle>
          <Navbar.Collapse id="navbar-default">
            <Nav className="ms-auto">
              <div className="ms-6 mt-3 mt-lg-0">
                <div className="d-flex align-items-center">
                  {/* <DarkLightMode className="me-2" /> */}
                  <Nav.Link
                    as={Link}
                    href="/contact"
                    bsPrefix="btn"
                    className="btn btn-white me-2">
                    Submit Your Startup
                  </Nav.Link>
                  <Nav.Link
                    as={Link}
                    href="/sign-in"
                    bsPrefix="btn"
                    className="btn btn-primary">
                    Start Investing
                  </Nav.Link>
                </div>
              </div>
              <span className={`ms-auto mt-3 mt-lg-0`}></span>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>
    </Fragment>
  );
}

export default NavbarJobPages