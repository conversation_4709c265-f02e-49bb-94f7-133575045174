// import node module libraries
import { Fragment, useState } from 'react';
import PropTypes from 'prop-types';
import {
	Image,
	Navbar,
	Nav,
	Container
} from 'react-bootstrap';
import Link from 'next/link';

const NavbarMegaMenu = () => {
	const [expandedMenu, setExpandedMenu] = useState(false);

	return (
    <Fragment>
      <Navbar
        onToggle={(collapsed) => setExpandedMenu(collapsed)}
        expanded={expandedMenu}
        expand="lg"
        fixed="top"
        shadow="none"
        className="navbar-default shadow-none px-0">
        <Container>
          <div className="d-flex">
            <Navbar.Brand as={Link} href="/">
              <Image src="/images/brand/logo/logo.svg" alt="" />
            </Navbar.Brand>
            {/* <CategoriesDropDown /> */}
          </div>

          {/* Right side quick / shortcut menu  */}
          <div className="ms-auto d-block d-sm-none">
            <div className="d-flex align-items-center">
              {/* <DarkLightMode /> */}
              <Link href="/platforms" className="btn btn-outline-dark ms-3">
                View Platforms
              </Link>
              {/* <Link href="/sign-in" className="btn btn-dark ms-1">
                  Join Our Family
                </Link> */}
            </div>
          </div>
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav>
              {/* <MegaMenu /> */}
              <Link href="/platforms" className="btn btn-white">
                Platforms
              </Link>
              {/* <Link href="/services" className="btn btn-white">
                Services
              </Link> */}
              {/* <Link href="/how" className="btn btn-white">
                How It Invest
              </Link> */}
              {/* <Link href="#" className="btn btn-white">
                Pricing
              </Link> */}
              <Link href="/about" className="btn btn-white">
                About Us
              </Link>
              <Link href="/career" className="btn btn-white">
                Careers
              </Link>
              {/* <Link href="/blog" className="btn btn-white">
                Blog
              </Link> */}
            </Nav>

            {/* Right side quick / shortcut menu  */}
            <div className="ms-auto mt-3 mt-lg-0 px-0">
              <div className="d-flex align-items-center">
                {/* <DarkLightMode /> */}
                <Link href="/register" className="btn btn-outline-dark">
                  Register As Investor
                </Link>
                {/* <Link href="/login" className="btn btn-dark ms-3">
                  Sign In
                </Link> */}
              </div>
            </div>
            {/* end of right side quick / shortcut menu  */}
          </Navbar.Collapse>
        </Container>
      </Navbar>
    </Fragment>
  );
};

// Specifies the default values for props
NavbarMegaMenu.defaultProps = {
	headerstyle: 'navbar-default',
	login: false
};

// Typechecking With PropTypes
NavbarMegaMenu.propTypes = {
	headerstyle: PropTypes.string,
	login: PropTypes.bool
};

export default NavbarMegaMenu;
