// import node module libraries
import React, { Fragment, useEffect } from 'react';

// import layouts
import NavbarLanding from './navbars/NavbarLanding';
import FooterWithLinks from './footers/FooterWithLinks';
import NavbarMegaMenu from './navbars/mega-menu/NavbarMegaMenu';
import NavbarJobPages from './navbars/NavbarJobPages';
import FooterCenter from './footers/FooterCenter';
import FooterWithSocialIcons from './footers/FooterWithSocialIcons';

const DefaultLayout = (props) => {
	useEffect(() => {
		document.body.className = 'bg-light';
	});
	return (
    <Fragment>
      <NavbarMegaMenu />
      <main className='mt-6'>{props.children}</main>
      {/* <Footer bgColor="bg-light" /> */}
      <FooterWithLinks />
    </Fragment>
  );
};

export default DefaultLayout;
