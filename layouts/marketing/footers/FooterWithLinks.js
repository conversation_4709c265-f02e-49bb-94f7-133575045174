// import node module libraries
import Link from 'next/link';
import { Row, Col, Image, Container, ListGroup } from 'react-bootstrap';

// import MDI icons
import Icon from '@mdi/react';
import { mdiFacebook, mdiTwitter, mdiInstagram } from '@mdi/js';

const FooterWithLinks = () => {
	return (
    <footer className="pt-lg-10 pt-5 footer bg-white">
      <Container>
        <Row>
          <Col lg={5} md={4} sm={12}>
            {/* about company  */}
            <div className="mb-4">
              <Image
                src="/images/brand/logo/logo.svg"
                alt=""
                className="logo-inverse"
              />
              <div className="mt-4">
                <p>
                  Transform your business with <PERSON>’s comprehensive financial
                  management services.
                </p>
                {/* social media */}
                <div className="fs-4 mt-4">
                  <Link href="#" className="mdi mdi-facebook text-muted me-2">
                    <Icon path={mdiFacebook} size={1.0} />
                  </Link>
                  <Link href="#" className="mdi mdi-twitter text-muted me-2">
                    <Icon path={mdiTwitter} size={1.0} />
                  </Link>
                  <Link href="#" className="mdi mdi-instagram text-muted ">
                    <Icon path={mdiInstagram} size={1.0} />
                  </Link>
                </div>
              </div>
            </div>
          </Col>
          <Col lg={2} md={3} sm={6}>
            <div className="mb-4">
              {/* list */}
              <h3 className="fw-bold mb-3">Company</h3>
              <ListGroup
                as="ul"
                bsPrefix="list-unstyled"
                className="nav nav-footer flex-column nav-x-0">
                {/* <ListGroup.Item as="li" bsPrefix=" ">
                  <Link href="/startup" className="nav-link">
                    Startups
                  </Link>
                </ListGroup.Item> */}
                <ListGroup.Item as="li" bsPrefix=" ">
                  <Link href="/how" className="nav-link">
                    How It Works
                  </Link>
                </ListGroup.Item>
                {/* <ListGroup.Item as="li" bsPrefix=" ">
                  <Link href="/invest" className="nav-link">
                    Make Investment
                  </Link>
                </ListGroup.Item> */}
                <ListGroup.Item as="li" bsPrefix=" ">
                  <Link href="/about" className="nav-link">
                    About Us
                  </Link>
                </ListGroup.Item>

                {/* <ListGroup.Item as="li" bsPrefix=" ">
                  <Link href="/shareholders" className="nav-link">
                    Shareholders
                  </Link>
                </ListGroup.Item> */}
                <ListGroup.Item as="li" bsPrefix=" ">
                  <Link href="/career" className="nav-link">
                    Careers
                  </Link>
                </ListGroup.Item>
                {/* <ListGroup.Item as="li" bsPrefix=" ">
                  <Link href="/blogs" className="nav-link">
                    Blogs
                  </Link>
                </ListGroup.Item> */}
              </ListGroup>
            </div>
          </Col>
          <Col lg={2} md={3} sm={6}>
            <div className="mb-4">
              <h3 className="fw-bold mb-3">Legal</h3>
              <ListGroup
                as="ul"
                bsPrefix="list-unstyled"
                className="nav nav-footer flex-column nav-x-0">
                <ListGroup.Item as="li" bsPrefix=" ">
                  <Link href="#" className="nav-link">
                    Terms of Service
                  </Link>
                </ListGroup.Item>
                <ListGroup.Item as="li" bsPrefix=" ">
                  <Link href="#" className="nav-link">
                    Privacy Policy
                  </Link>
                </ListGroup.Item>
                <ListGroup.Item as="li" bsPrefix=" ">
                  <Link href="/shareholders" className="nav-link">
                    Cookie Policy
                  </Link>
                </ListGroup.Item>
              </ListGroup>
            </div>
          </Col>
          <Col lg={3} md={12} sm={12}>
            {/* contact info */}
            <div className="mb-4">
              <h3 className="fw-bold mb-4">Get in touch</h3>
              {/* <p>
                WeWork, One Central 8th Floor, Trade Centre, Trade Centre 2,
                Dubai - United Arab Emirates
              </p> */}
              <p>
                WeWork, RMZ Latitude Commercial, 10th floor, Hebbal, Bengaluru,
                KA, India - 560024
              </p>
              <p className="mb-1">
                Email: <Link href="#"><EMAIL></Link>
              </p>
              {/* <p>
                Phone:{" "}
                <span className="text-dark fw-semi-bold">(*************</span>
              </p> */}
            </div>
          </Col>
        </Row>
        <Row className="align-items-center g-0 border-top py-2 mt-6">
          {/* Desc  */}
          <Col>
            <span>© 2024 Sharif Ventures Private Limited</span>
          </Col>
        </Row>
      </Container>
    </footer>
  );
};

export default FooterWithLinks;
