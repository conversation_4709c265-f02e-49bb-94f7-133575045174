// import node module libraries
import Link from 'next/link';
import { Fragment } from 'react';
import { useMediaQuery } from 'react-responsive';
import {
    Row,
    Col,
    Image,
    Dropdown,
    ListGroup,
} from 'react-bootstrap';

// simple bar scrolling used for notification item scrolling
import SimpleBar from 'simplebar-react';
import 'simplebar/dist/simplebar.min.css';

// import widget/custom components
import { GKTippy } from 'widgets';

// import custom components
import DotBadge from 'components/bootstrap/DotBadge';
import DarkLightMode from 'layouts/DarkLightMode';

// import data files
import NotificationList from 'data/Notification';

// import hooks
import useMounted from 'hooks/useMounted';

const QuickMenu = () => {

    const hasMounted = useMounted();

    const isDesktop = useMediaQuery({ query: '(min-width: 1224px)' });

    const Notifications = () => {
        return (
            <SimpleBar style={{ maxHeight: '300px' }}>
                <ListGroup variant="flush">
                    {NotificationList.map(function (item, index) {
                        return (
                            <ListGroup.Item className={index === 0 ? 'bg-light' : ''} key={index}>
                                <Row>
                                    <Col>
                                        <Link href="#" className="text-body">
                                            <div className="d-flex">
                                                <Image src={item.image} alt="" className="avatar-md rounded-circle" />
                                                <div className="ms-3">
                                                    <h5 className="fw-bold mb-1">{item.sender}</h5>
                                                    <p className="mb-3">{item.message}</p>
                                                    <span className="fs-6 text-muted">
                                                        <span>
                                                            <span className="fe fe-thumbs-up text-success me-1"></span>
                                                            {item.date}
                                                        </span>
                                                        <span className="ms-1">{item.time}</span>
                                                    </span>
                                                </div>
                                            </div>
                                        </Link>
                                    </Col>
                                    <Col xs="auto" className="text-center me-2">
                                        <GKTippy content="Mark as unread" >
                                            <Link href="#"><DotBadge bg="secondary" ></DotBadge></Link>
                                        </GKTippy>
                                    </Col>
                                </Row>
                            </ListGroup.Item>
                        );
                    })}
                </ListGroup>
            </SimpleBar>
        );
    }

    return (
      <Fragment>
        <DarkLightMode />
        <ListGroup
          as="ul"
          bsPrefix="navbar-nav"
          className="navbar-right-wrap ms-2 d-flex nav-top-wrap">
          <Dropdown as="li">
            <Dropdown.Toggle
              as="a"
              bsPrefix=" "
              id="dropdownNotification"
              className="text-dark icon-notifications me-lg-1  btn btn-light btn-icon rounded-circle indicator indicator-primary text-muted">
              <i className="fe fe-bell"></i>
            </Dropdown.Toggle>
            <Dropdown.Menu
              className="dashboard-dropdown notifications-dropdown dropdown-menu-lg dropdown-menu-end mt-4 py-0"
              aria-labelledby="dropdownNotification"
              align="end"
              show={hasMounted && isDesktop ? true : false}>
              <Dropdown.Item className="mt-3" bsPrefix=" " as="div">
                <div className="border-bottom px-3 pt-0 pb-3 d-flex justify-content-between align-items-end">
                  <span className="h4 mb-0">Notifications</span>
                  <Link href="/" className="text-muted">
                    <span className="align-middle">
                      <i className="fe fe-settings me-1"></i>
                    </span>
                  </Link>
                </div>
                <Notifications />
                <div className="border-top px-3 pt-3 pb-3">
                  <Link
                    href="/dashboard/notification-history"
                    className="text-link fw-semi-bold">
                    See all Notifications
                  </Link>
                </div>
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
          <Dropdown as="li">
            <Dropdown.Toggle
              as="a"
              bsPrefix=" "
              id="dropdownNotification"
              className="text-dark icon-notifications me-lg-1  btn btn-light btn-icon rounded-circle indicator indicator-primary text-muted">
              <i className="fe fe-message-square"></i>
            </Dropdown.Toggle>
            <Dropdown.Menu
              className="dashboard-dropdown notifications-dropdown dropdown-menu-lg dropdown-menu-end mt-4 py-0"
              aria-labelledby="dropdownNotification"
              align="end"
              show={hasMounted && isDesktop ? true : false}>
              <Dropdown.Item className="mt-3" bsPrefix=" " as="div">
                <div className="border-bottom px-3 pt-0 pb-3 d-flex justify-content-between align-items-end">
                  <span className="h4 mb-0">Messages</span>
                  <Link href="/" className="text-muted">
                    <span className="align-middle">
                      <i className="fe fe-settings me-1"></i>
                    </span>
                  </Link>
                </div>
                <Notifications />
                <div className="border-top px-3 pt-3 pb-3">
                  <Link
                    href="/dashboard/messages"
                    className="text-link fw-semi-bold">
                    See all Messages
                  </Link>
                </div>
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
          <Dropdown as="li">
            <Dropdown.Toggle
              as="a"
              bsPrefix=" "
              id="dropdownNotification"
              className="text-dark icon-notifications me-lg-1  btn btn-light btn-icon rounded-circle indicator indicator-primary text-muted">
              <i className="fe fe-mail"></i>
            </Dropdown.Toggle>
            <Dropdown.Menu
              className="dashboard-dropdown notifications-dropdown dropdown-menu-lg dropdown-menu-end mt-4 py-0"
              aria-labelledby="dropdownNotification"
              align="end"
              show={hasMounted && isDesktop ? true : false}>
              <Dropdown.Item className="mt-3" bsPrefix=" " as="div">
                <div className="border-bottom px-3 pt-0 pb-3 d-flex justify-content-between align-items-end">
                  <span className="h4 mb-0">Mail</span>
                  <Link href="/" className="text-muted">
                    <span className="align-middle">
                      <i className="fe fe-settings me-1"></i>
                    </span>
                  </Link>
                </div>
                <Notifications />
                <div className="border-top px-3 pt-3 pb-3">
                  <Link
                    href="/dashboard/mail"
                    className="text-link fw-semi-bold">
                    See all Emails
                  </Link>
                </div>
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
          <Dropdown as="li">
            <Dropdown.Toggle
              as="a"
              bsPrefix=" "
              id="dropdownNotification"
              className="text-dark icon-notifications me-lg-1  btn btn-light btn-icon rounded-circle indicator indicator-primary text-muted">
              <i className="fe fe-check-square"></i>
            </Dropdown.Toggle>
            <Dropdown.Menu
              className="dashboard-dropdown notifications-dropdown dropdown-menu-lg dropdown-menu-end mt-4 py-0"
              aria-labelledby="dropdownNotification"
              align="end"
              show={hasMounted && isDesktop ? true : false}>
              <Dropdown.Item className="mt-3" bsPrefix=" " as="div">
                <div className="border-bottom px-3 pt-0 pb-3 d-flex justify-content-between align-items-end">
                  <span className="h4 mb-0">Tasks</span>
                  <Link href="/" className="text-muted">
                    <span className="align-middle">
                      <i className="fe fe-settings me-1"></i>
                    </span>
                  </Link>
                </div>
                <Notifications />
                <div className="border-top px-3 pt-3 pb-3">
                  <Link
                    href="/dashboard/task"
                    className="text-link fw-semi-bold">
                    See all Tasks
                  </Link>
                </div>
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
          <Dropdown as="li">
            <Dropdown.Toggle
              as="a"
              bsPrefix=" "
              id="dropdownNotification"
              className="text-dark icon-notifications me-lg-1  btn btn-light btn-icon rounded-circle indicator indicator-primary text-muted">
              <i className="fe fe-calendar"></i>
            </Dropdown.Toggle>
            <Dropdown.Menu
              className="dashboard-dropdown notifications-dropdown dropdown-menu-lg dropdown-menu-end mt-4 py-0"
              aria-labelledby="dropdownNotification"
              align="end"
              show={hasMounted && isDesktop ? true : false}>
              <Dropdown.Item className="mt-3" bsPrefix=" " as="div">
                <div className="border-bottom px-3 pt-0 pb-3 d-flex justify-content-between align-items-end">
                  <span className="h4 mb-0">Calendar</span>
                  <Link href="/" className="text-muted">
                    <span className="align-middle">
                      <i className="fe fe-settings me-1"></i>
                    </span>
                  </Link>
                </div>
                <Notifications />
                <div className="border-top px-3 pt-3 pb-3">
                  <Link
                    href="/dashboard/calendar"
                    className="text-link fw-semi-bold">
                    See all Events
                  </Link>
                </div>
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
          <Dropdown as="li" className="ms-2">
            <Dropdown.Toggle
              as="a"
              bsPrefix=" "
              className="rounded-circle"
              id="dropdownUser">
              <div className="avatar avatar-md">
                <Image
                  alt="avatar"
                  src="/images/avatar/shawaz.jpg"
                  className="rounded"
                />
              </div>
            </Dropdown.Toggle>
            <Dropdown.Menu
              className="dashboard-dropdown dropdown-menu-end mt-4 py-0"
              align="end"
              aria-labelledby="dropdownUser"
              show={hasMounted && isDesktop ? true : false}>
              <Dropdown.Item className="mt-3">
                <div className="d-flex">
                  <div className="avatar avatar-md avatar-indicators avatar-online">
                    <Image
                      alt="avatar"
                      src="/images/avatar/shawaz.jpg"
                      className="rounded-circle"
                    />
                  </div>
                  <div className="ms-3 lh-1">
                    <h5 className="mb-1">Shawaz Sharif</h5>
                    <p className="mb-0 text-muted"><EMAIL></p>
                  </div>
                </div>
              </Dropdown.Item>
              <Dropdown.Divider />
              <Dropdown.Item className="mb-3">
                <Link href={'/home'}>
                  <i className="fe fe-power me-2"></i> Sign Out
                </Link>
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </ListGroup>
      </Fragment>
    );
}

export default QuickMenu;