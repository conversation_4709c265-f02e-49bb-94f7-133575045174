// import node module libraries
import { Card, <PERSON>, Button } from 'react-bootstrap';

// import widget/custom components
import { GKTagsInput } from 'widgets';

const Settings = (props) => {
	const { previous } = props;

	return (
		<Form>
			{/* Card */}
			<Card className="mb-3  border-0">
				<Card.Header className="border-bottom px-4 py-3">
					<h4 className="mb-0">Requirements</h4>
				</Card.Header>
				{/* Card body */}
				<Card.Body>
					<GKTagsInput defaulttags={['jQuery', 'bootstrap']} />
				</Card.Body>
			</Card>
			<div className="d-flex justify-content-between mb-22">
				{/* Button */}
				<Button variant="secondary" onClick={previous}>
					Previous
				</Button>
				<Button variant="danger">Submit For Review</Button>
			</div>
		</Form>
	);
};
export default Settings;
