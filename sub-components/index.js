/**
 * The folder sub-components contains sub component of all the pages,
 * so here you will find folder names which are listed in root pages.
 */

/** 
* MARKETING SUB COMPONENTS
*/

// sub components for marketing/compare-plan.js page
import ComparePlansTable from 'sub-components/compare-plan/ComparePlansTable';
import DeveloperGeeks from 'sub-components/compare-plan/DeveloperGeeks';
import FAQs from 'sub-components/compare-plan/FAQs';
import PricingPlans from 'sub-components/compare-plan/PricingPlans';

// sub components for marketing/course-category.js page
import PopularInstructorCard from 'sub-components/course-category/PopularInstructorCard';

// sub components for marketing/career/career-list.js page
import HeroCollage from 'sub-components/career/career-list/HeroCollage';
import CareerPositions from 'sub-components/career/career-list/CareerPositions';

// sub components for marketing/career/career-single.js page
import ApplyForm from 'sub-components/career/career-single/ApplyForm';

// sub components for marketing/career/overview.js page
import CareerAtGeeks from 'sub-components/career/overview/CareerAtGeeks';
import Collage from 'sub-components/career/overview/Collage';
import GeeksCulture from 'sub-components/career/overview/GeeksCulture';
import TeamMemberAtGeeks from 'sub-components/career/overview/TeamMemberAtGeeks';

// sub components for marketing/courses/course-filter-page.js page
import FilterOptions from 'sub-components/courses/course-filter-page/FilterOptions';
import CourseGridView from 'sub-components/courses/course-filter-page/CourseGridView';
import CourseListView from 'sub-components/courses/course-filter-page/CourseListView';

// sub components for marketing/courses/course-path.js page
import CoursePathCard from 'sub-components/courses/course-path/CoursePathCard';

// sub components for marketing/courses/course-path.js page
import CoursesTab from 'sub-components/courses/course-path-single/CoursesTab';
import AboutTab from 'sub-components/courses/course-path-single/AboutTab';
import AuthorTab from 'sub-components/courses/course-path-single/AuthorTab';

// sub components for blog pages
import BlogCard from 'sub-components/blog/BlogCard';
import BlogCardFullWidth from 'sub-components/blog/BlogCardFullWidth';

// sub components for marketing/courses/course-single.js page
import ReviewsTab from 'sub-components/courses/course-single/ReviewsTab';
import DescriptionTab from 'sub-components/courses/course-single/DescriptionTab';
import TranscriptTab from 'sub-components/courses/course-single/TranscriptTab';
import FAQTab from 'sub-components/courses/course-single/FAQTab';

// sub components for marketing/help-center/faq.js page
import AllFAQsList from 'sub-components/help-center/faq/AllFAQsList';

// sub components for marketing/help-center/guide.js page
import ArticlesCategoryCard from 'sub-components/help-center/guide/ArticlesCategoryCard';

// sub components for marketing/help-center/guide.js page
import ContactSupportSection from 'sub-components/help-center/help-center-default/ContactSupportSection';
import HelpCenterFAQs from 'sub-components/help-center/help-center-default/HelpCenterFAQs';
import HeroGradientHeader from 'sub-components/help-center/help-center-default/HeroGradientHeader';

// sub components for marketing/help-center/support.js page
import SupportForm from 'sub-components/help-center/support/SupportForm';

// sub components for marketing - help-center - all pages
import HeaderBreadcrumb from 'sub-components/help-center/HeaderBreadcrumb';

// sub components for marketing/pricing.js page
import PricingCard from 'sub-components/pricing/PricingCard';

// sub components for marketing/landings/course-lead.js page
import HeroFormRight from 'sub-components/landings/course-lead/HeroFormRight';
import LeadTestimonialSection from 'sub-components/landings/course-lead/TestimonialSection';
import FeaturesWithBullets from 'sub-components/landings/course-lead/FeaturesWithBullets';
import CourseDescriptionSection from 'sub-components/landings/course-lead/CourseDescriptionSection';
import FAQsection from 'sub-components/landings/course-lead/FAQsection';
import YourInstructor from 'sub-components/landings/course-lead/YourInstructor';

// sub components for marketing/landings/landing-courses.js page
import CoursesFeatures4Columns from 'sub-components/landings/landing-courses/Features4Columns';
import BrowseCategories from 'sub-components/landings/landing-courses/BrowseCategories';
import WorldClassInstructors from 'sub-components/landings/landing-courses/WorldClassInstructors';
import HeroTyped from 'sub-components/landings/landing-courses/HeroTyped';
import BecomeInstructor from 'sub-components/landings/landing-courses/BecomeInstructor';
import CoursesTestimonialSection from 'sub-components/landings/landing-courses/TestimonialSection';

// sub components for marketing/landings/landing-sass.js page
import HeroGradient from 'sub-components/landings/landing-sass/HeroGradient';
import SASSFeatures4Columns from 'sub-components/landings/landing-sass/Features4Columns';
import AppIntegration from 'sub-components/landings/landing-sass/AppIntegration';
import CustomersTestimonials from 'sub-components/landings/landing-sass/CustomersTestimonials';
import SimplePricingPlans from 'sub-components/landings/landing-sass/SimplePricingPlans';
import HeroLeftImage from 'sub-components/landings/landing-sass/HeroLeftImage';
import HeroRightImage from 'sub-components/landings/landing-sass/HeroRightImage';

// sub components for marketing/landings/request-access.js page
import HeroFormCenter from 'sub-components/landings/request-access/HeroFormCenter';
import HeroFormLeft from 'sub-components/landings/request-access/HeroFormLeft';
import Features2Columns from 'sub-components/landings/request-access/Features2Columns';
import RequestAccessTestimonialSection from 'sub-components/landings/request-access/TestimonialSection';

// sub components for marketing/instructor/add-new-course.js page
import BasicInformation from 'sub-components/instructor/add-new-course/steps/BasicInformation';
import CoursesMedia from 'sub-components/instructor/add-new-course/steps/CoursesMedia';
import Curriculum from 'sub-components/instructor/add-new-course/steps/Curriculum';
import Settings from 'sub-components/instructor/add-new-course/steps/Settings';

// sub components for about.js page
import TeamGridRoundImages from 'sub-components/about/TeamGridRoundImages';
import JustifiedGallery from 'sub-components/about/JustifiedGallery';
import FeaturesList from 'sub-components/about/FeaturesList';
import HeroContent from 'sub-components/about/HeroContent';
import CTAButton from 'sub-components/about/CTAButton';
import Stat from 'sub-components/about/Stat';

// sub components for contact.js page
import ContactForm from 'sub-components/contact/ContactForm';

// sub components for home academy page ( home-academy.js - v2.0.0 )
import HeroAcademy from 'sub-components/landings/home-academy/HeroAcademy';
import AcademyStats from 'sub-components/landings/home-academy/AcademyStats';
import MostPopularCourses from 'sub-components/landings/home-academy/MostPopularCourses';
import BecomeAnInstructor from 'sub-components/landings/home-academy/BecomeAnInstructor';
import WhatCustomersSay from 'sub-components/landings/home-academy/WhatCustomersSay';

// sub components for jobs-> upload resume steps ( v2.0.0 )
import BasicDetails from 'sub-components/jobs/upload-resume/steps/BasicDetails';
import Employment from 'sub-components/jobs/upload-resume/steps/Employment';
import Education from 'sub-components/jobs/upload-resume/steps/Education';
import Job from 'sub-components/jobs/upload-resume/steps/Job';

// sub components for jobs -> landing page ( landing-job.js - v2.0.0 )
import BrowseJobCategories from 'sub-components/jobs/landing-job/BrowseJobCategories'
import CustomerStories from 'sub-components/jobs/landing-job/CustomerStories'
import FindYourDreamJob from 'sub-components/jobs/landing-job/FindYourDreamJob'
import HowItWorks from 'sub-components/jobs/landing-job/Process'
import LatestJobOpening from 'sub-components/jobs/landing-job/LatestJobOpening'
import TopCompanies from 'sub-components/jobs/landing-job/TopCompanies'

// sub components for jobs -> post a job ( post-a-job.js - v2.0.0 )
import JobPosterInfo from 'sub-components/jobs/post-a-job/JobPosterInfo';
import JobInfo from 'sub-components/jobs/post-a-job/JobInfo';
import CompanyInfo from 'sub-components/jobs/post-a-job/CompanyInfo';

// sub component for job search for all job pages( v2.0.0 )
import JobSearchBox from 'sub-components/jobs/JobSearchBox'

// sub component for job filtering for company list page ( v2.0.0 )
import CompanyFilters from 'sub-components/jobs/company-list/CompanyFilters'

// sub component for job -> comapny pages header tabs ( v2.0.0 )
import CompanyCommonHeaderTabs from 'sub-components/jobs/company/CommonHeaderTabs'

// sub components for jobs -> job listing ( grid and list view - v2.0.0 )
import JobFilters from 'sub-components/jobs/job-listing/JobFilters';
import JobsGridView from 'sub-components/jobs/job-listing/JobsGridView';
import JobsListView from 'sub-components/jobs/job-listing/JobsListView';

// sub component for quiz pages( v2.0.0 )
import Question from 'sub-components/quiz/Question';
import QuizPagination from 'sub-components/quiz/quiz-start/QuizPagination';
import QuizProgress from 'sub-components/quiz/quiz-start/QuizProgress';
import QuizTimer from 'sub-components/quiz/quiz-start/QuizTimer';

// sub component for porfolio pages ( v2.2.0 )
import PortfolioItem from 'sub-components/portfolio/PortfolioItem';

// sub components for education landing page ( v2.2.0 )
import EducationHeroRightImage from 'sub-components/landings/education/HeroRightImage';
import ExploreSkillCourses from 'sub-components/landings/education/ExploreSkillCourses';
import BuildingSkills from 'sub-components/landings/education/BuildingSkills';
import LearnLatestSkills from 'sub-components/landings/education/LearnLatestSkills';
import EducationFeaturesWithBullets from 'sub-components/landings/education/FeaturesWithBullets';
import UpcomingWebinars from 'sub-components/landings/education/UpcomingWebinars';
import FindRightCourse from 'sub-components/landings/education/FindRightCourse';

/** 
* DASHBOARD SUB COMPONENTS
*/

// sub components for dashboard/overview.js page
import PopularInstructor from 'sub-components/dashboard/overview/PopularInstructor';
import RecentCourses from 'sub-components/dashboard/overview/RecentCourses';
import Activity from 'sub-components/dashboard/overview/Activity';

// sub components for dashboard/analytics.js page
import MostViewPages from 'sub-components/dashboard/analytics/MostViewPages';
import Browsers from 'sub-components/dashboard/analytics/Browsers';
import SocialMediaTraffic from 'sub-components/dashboard/analytics/SocialMediaTraffic';

// sub components for dashboard/courses/all-courses.js page
import CoursesTable from 'sub-components/dashboard/all-courses/CoursesTable';

// sub components for dashboard/courses/courses-category.js page
import AddNewCategoryPopup from 'sub-components/dashboard/courses-category/AddNewCategoryPopup';

// sub components for dashboard/user/instructor.js page
import InstructorsGridView from 'sub-components/dashboard/user/InstructorsGridCard';
import InstructorsListItems from 'sub-components/dashboard/user/InstructorsListItems';

// sub components for dashboard/user/students.js page
import StudentsGridCard from 'sub-components/dashboard/user/StudentsGridCard';
import StudentsListItems from 'sub-components/dashboard/user/StudentsListItems';

// sub components for /dashboard/cms/all-posts.js page
import PostsTable from 'sub-components/dashboard/cms/all-posts/PostsTable';

// sub components for /dashboard/projects/create-project.js page
import CreateProjectForm from 'sub-components/dashboard/projects/create-project/CreateProjectForm';

// sub components for /dashboard/projects/grid.js page
import ProjectCard from 'sub-components/dashboard/projects/grid/ProjectCard';

// sub components for /dashboard/projects/list.js page
import ProjectListTable from 'sub-components/dashboard/projects/list/ProjectListTable';

// sub components for /dashboard/projects/* pages
import OffcanvasCreateProjectForm from 'sub-components/dashboard/projects/OffcanvasCreateProjectForm';

// sub components for /dashboard/projects/single/* pages
import CommonHeaderTabs from 'sub-components/dashboard/projects/single/CommonHeaderTabs';

// sub components for /dashboard/projects/single/budget page
import BudgetCard from 'sub-components/dashboard/projects/single/budget/BudgetCard';
import ExpensesChartCard from 'sub-components/dashboard/projects/single/budget/ExpensesChartCard';
import BudgetCategoryCard from 'sub-components/dashboard/projects/single/budget/BudgetCategoryCard';
import BudgetDetailsCard from 'sub-components/dashboard/projects/single/budget/BudgetDetailsCard';

// sub components for /dashboard/projects/single/overview pages
import ProjectSummary from 'sub-components/dashboard/projects/single/overview/ProjectSummary';
import BudgetSection from 'sub-components/dashboard/projects/single/overview/BudgetSection';
import UpcomingDeadlines from 'sub-components/dashboard/projects/single/overview/UpcomingDeadlines';
import LaunchDate from 'sub-components/dashboard/projects/single/overview/LaunchDate';
import OverallProgressChart from 'sub-components/dashboard/projects/single/overview/OverallProgressChart';
import RecentActivity from 'sub-components/dashboard/projects/single/overview/RecentActivity';

// sub components for /dashboard/projects/single/task pages
import TaskStats from 'sub-components/dashboard/projects/single/task/TaskStats';
import TaskSummaryChart from 'sub-components/dashboard/projects/single/task/TaskSummaryChart';
import TaskCompletionStatusChart from 'sub-components/dashboard/projects/single/task/TaskCompletionStatusChart';
import TaskbySectionsChart from 'sub-components/dashboard/projects/single/task/TaskbySectionsChart';
import UpcomingTaskList from 'sub-components/dashboard/projects/single/task/UpcomingTaskList';

// sub components for /dashboard/projects/single/files pages
import FilesTable from 'sub-components/dashboard/projects/single/files/FilesTable';

// sub components for /dashboard/projects/single/summary pages
import Assignee from 'sub-components/dashboard/projects/single/summary/Assignee';
import ProjectSchedule from 'sub-components/dashboard/projects/single/summary/ProjectSchedule';
import ProjectProgress from 'sub-components/dashboard/projects/single/summary/ProjectProgress';
import ProjectDescription from 'sub-components/dashboard/projects/single/summary/ProjectDescription';

// sub components for /dashboard/projects/single/team pages
import TeamGrid from 'sub-components/dashboard/projects/single/team/TeamGrid';

// sub components for /dashboard/chat page
import Sidebar from 'sub-components/dashboard/chat/sidebar/Sidebar';
import ChatBox from 'sub-components/dashboard/chat/chatbox/ChatBox';

// sub components for /dashboard/task-kanban page
import KanbanColumn from 'sub-components/dashboard/task-kanban/KanbanColumn';

// sub components for /dashboard/calendar page v2.1.0
import AddEditEvent from 'sub-components/dashboard/calendar/AddEditEvent';

// sub components for /dashboard/mail pages v2.1.0
import MailSidebar from 'sub-components/dashboard/mail-app/MailSidebar';
import MailRow from 'sub-components/dashboard/mail-app/mail/MailRow';
import MailDetailsBody from 'sub-components/dashboard/mail-app/mail-details/MailDetailsBody';
import MailDetailsFooter from 'sub-components/dashboard/mail-app/mail-details/MailDetailsFooter';
import MailDetailsHeader from 'sub-components/dashboard/mail-app/mail-details/MailDetailsHeader';

// sub components for datatable page v2.2.0
import Pagination from 'sub-components/dashboard/data-table/Pagination';


// sub components for ecommerce pages v2.2.0
import ColorOptions from 'sub-components/dashboard/ecommerce/ColorOptions';
import ProductBriefInfo from 'sub-components/dashboard/ecommerce/ProductBriefInfo';
import ProductCard from 'sub-components/dashboard/ecommerce/ProductCard';
import ProductCarousel from 'sub-components/dashboard/ecommerce/ProductCarousel';
import ProductDetailsAccordion from 'sub-components/dashboard/ecommerce/ProductDetailsAccordion';
import ProductGallery from 'sub-components/dashboard/ecommerce/ProductGallery';
import ProductGallery2 from 'sub-components/dashboard/ecommerce/ProductGallery2';
import ProductGridView from 'sub-components/dashboard/ecommerce/ProductGridView';
import RatingsReviews from 'sub-components/dashboard/ecommerce/RatingsReviews';
import ProductsTable from 'sub-components/dashboard/ecommerce/ProductsTable';
import OrdersTable from 'sub-components/dashboard/ecommerce/orders/OrdersTable';
import OrderSummary from 'sub-components/dashboard/ecommerce/checkout/OrderSummary';
import BillingInformation from 'sub-components/dashboard/ecommerce/checkout/steps/BillingInformation';
import PaymentSelection from 'sub-components/dashboard/ecommerce/checkout/steps/PaymentSelection';
import ShippingInformation from 'sub-components/dashboard/ecommerce/checkout/steps/ShippingInformation';

export {
   AboutTab,
   Activity,
   AddNewCategoryPopup,
   AllFAQsList,
   AppIntegration,
   ApplyForm,
   ArticlesCategoryCard,
   Assignee,
   AuthorTab,
   BasicInformation,
   BecomeInstructor,
   BlogCard,
   BlogCardFullWidth,
   BrowseCategories,
   Browsers,
   BudgetCard,
   BudgetCategoryCard,
   BudgetDetailsCard,
   BudgetSection,
   CareerAtGeeks,
   CareerPositions,
   ChatBox,
   Collage,
   CommonHeaderTabs,
   ComparePlansTable,
   ContactForm,
   ContactSupportSection,
   CourseDescriptionSection,
   CourseGridView,
   CourseListView,
   CoursePathCard,
   CoursesFeatures4Columns,
   CoursesMedia,
   CoursesTab,
   CoursesTable,
   CoursesTestimonialSection,
   CreateProjectForm,
   CTAButton,
   Curriculum,
   CustomersTestimonials,
   DescriptionTab,
   DeveloperGeeks,
   ExpensesChartCard,
   FAQs,
   FAQsection,
   FAQTab,
   Features2Columns,
   FeaturesList,
   FeaturesWithBullets,
   FilesTable,
   FilterOptions,
   GeeksCulture,
   HeaderBreadcrumb,
   HelpCenterFAQs,
   HeroCollage,
   HeroContent,
   HeroFormCenter,
   HeroFormLeft,
   HeroFormRight,
   HeroGradient,
   HeroGradientHeader,
   HeroLeftImage,
   HeroRightImage,
   HeroTyped,
   InstructorsGridView,
   InstructorsListItems,
   JustifiedGallery,
   KanbanColumn,
   LaunchDate,
   LeadTestimonialSection,
   MostViewPages,
   OffcanvasCreateProjectForm,
   OverallProgressChart,
   PopularInstructor,
   PopularInstructorCard,
   PostsTable,
   PricingCard,
   PricingPlans,
   ProjectCard,
   ProjectDescription,
   ProjectListTable,
   ProjectProgress,
   ProjectSchedule,
   ProjectSummary,
   RecentActivity,
   RecentCourses,
   RequestAccessTestimonialSection,
   ReviewsTab,
   SASSFeatures4Columns,
   Settings,
   Sidebar,
   SimplePricingPlans,
   SocialMediaTraffic,
   Stat,
   StudentsGridCard,
   StudentsListItems,
   SupportForm,
   TaskbySectionsChart,
   TaskCompletionStatusChart,
   TaskStats,
   TaskSummaryChart,
   TeamGrid,
   TeamGridRoundImages,
   TeamMemberAtGeeks,
   TranscriptTab,
   UpcomingDeadlines,
   UpcomingTaskList,
   WorldClassInstructors,
   YourInstructor,

   BasicDetails, Employment, Education, Job,
   BrowseJobCategories, CustomerStories, FindYourDreamJob, HowItWorks, LatestJobOpening, TopCompanies,
   JobSearchBox, CompanyFilters, CompanyCommonHeaderTabs,
   JobPosterInfo, JobInfo, CompanyInfo,
   JobFilters, JobsGridView, JobsListView,
   HeroAcademy, AcademyStats, MostPopularCourses, BecomeAnInstructor, WhatCustomersSay,
   Question, QuizProgress, QuizPagination, QuizTimer,

   AddEditEvent,
   MailSidebar, MailRow, MailDetailsBody, MailDetailsFooter, MailDetailsHeader,

   PortfolioItem,

   // education landing page sub components
   EducationHeroRightImage,
   ExploreSkillCourses,
   BuildingSkills,
   LearnLatestSkills,
   EducationFeaturesWithBullets,
   UpcomingWebinars,
   FindRightCourse,

   Pagination,
   ColorOptions,
   ProductBriefInfo,
   ProductCard,
   ProductDetailsAccordion,
   ProductGallery,
   ProductGallery2,
   ProductGridView,
   RatingsReviews,
   ProductsTable,
   ProductCarousel,

   OrdersTable,

   OrderSummary, BillingInformation, PaymentSelection, ShippingInformation

}
