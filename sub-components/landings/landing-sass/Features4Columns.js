// Section : Features
// Style : Four Columns Features Section

// import node module libraries
import { Col, Row, Container } from 'react-bootstrap';

// import MDI icons
import { mdiFlash, mdiLayers, mdiCellphone, mdiInfinity } from '@mdi/js';

// import widget/custom components
import {FeatureTopIcon3} from 'widgets';


const Features4Columns = () => {
	const features = [
    {
      id: 1,
      icon: mdiFlash,
      title: "Valuation",
      description: `Review the latest company valuation.`,
    },
    {
      id: 2,
      icon: mdiLayers,
      title: "Current Sales",
      description: `Get real-time updates on sales performance.`,
    },
    {
      id: 3,
      icon: mdiCellphone,
      title: "Customer Base",
      description: `Understand the reach and scale of each startup.`,
    },
    {
      id: 4,
      icon: mdiInfinity,
      title: "Annual Growth",
      description: `Assess growth rates for better insight into potential returns.`,
    },
  ];

	return (
		<section className="py-lg-20 pt-8 bg-white">
			<Container>
				<Row>
					{features.map((item, index) => {
						return (
							<Col lg={3} md={6} sm={12} key={index}>
								<FeatureTopIcon3 item={item} />
							</Col>
						);
					})}
				</Row>
			</Container>
		</section>
	);
};

export default Features4Columns;
