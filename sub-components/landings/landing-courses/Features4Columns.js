// Section : Features
// Style : Four Columns Features Section

// import node module libraries
import Link from 'next/link';
import { Col, Row, Container } from 'react-bootstrap';

// import widget/custom components
import {SectionHeadingCenter, FeatureTopIcon } from 'widgets';

const Features4Columns = () => {
	const title = "Public Information for Investors";
	const subtitle = 'GET FREE ACCESS TO';
	const description = `Before investing, access key data to help you make an informed decision:`;

	const features = [
    {
      id: 1,
      icon: "credit-card",
      title: "Current Sales",
      description: `Get real-time updates on sales performance.`,
    },
    {
      id: 2,
      icon: "user",
      title: "Customer Base",
      description: `Understand the reach and scale of each startup.`,
    },
    {
      id: 3,
      icon: "pie-chart",
      title: "Valuation",
      description: `Review the latest company valuation towards the target.`,
    },
    {
      id: 4,
      icon: "trending-up",
      title: "Annual Growth",
      description: `Assess growth rates for better insight into potential returns.`,
    },
  ];

	return (
    <section className="py-8 py-lg-18 bg-light">
      <Container>
        <SectionHeadingCenter
          title={title}
          description={description}
          subtitle={subtitle}
        />
        <Row>
          {features.map((item, index) => {
            return (
              <Col lg={3} md={6} sm={12} key={index}>
                <FeatureTopIcon item={item} />
              </Col>
            );
          })}
        </Row>
        {/* <Row>
          <Col md={12} className="mt-10 text-center">
            <Link href="/startup" className="btn btn-dark">
              View Startups
            </Link>
          </Col>
        </Row> */}
      </Container>
    </section>
  );
};

export default Features4Columns;
