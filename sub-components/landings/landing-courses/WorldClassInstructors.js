// import node module libraries
import { Col, Row, Container } from 'react-bootstrap';
import { useMediaQuery } from 'react-responsive';
import Link from 'next/link';

// import widget/custom components
import { SectionHeadingCenter } from 'widgets';

// import sub components
import InstructorCard from './InstructorCard';

// import data files
import { InstructorsList } from 'data/courses/LandingCoursesData';

const WorldClassInstructors = () => {
	const title = "Investing in Platforms";
	const subtitle = 'Investor Onboarding Process';
	const description = `A step-by-step guide for investors to start investing and earning with our platforms.`;

	const isLaptop = useMediaQuery({ minWidth: 1024, maxWidth: 1445 });

	return (
		<section className="py-8 py-lg-16 bg-light-gradient-top bg-white">
			<Container>
				<SectionHeadingCenter
					title={title}
					description={description}
					subtitle={subtitle}
				/>
				<Row>
					{InstructorsList.map((item, index) => (
						<Col
							key={index}
							xl={4}
							lg={4}
							md={6}
							sm={12}
							className={`${isLaptop && index === 3 ? 'd-lg-none d-xl-block' : ''
								}`}
						>
							<InstructorCard item={item} />
						</Col>
					))}
				</Row>
				<Row>
					<Col md={12} className="mt-3 text-center">
						<Link href="/invest" className="btn btn-dark">
							Start Process
						</Link>
					</Col>
				</Row>
			</Container>
		</section>
	);
};

export default WorldClassInstructors;
