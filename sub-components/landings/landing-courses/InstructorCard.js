// import node module libraries
import React from 'react';
import { Card } from 'react-bootstrap';
import Link from 'next/link';
import PropTypes from 'prop-types';

// import MDI icons
import Icon from '@mdi/react';
import { mdiStar } from '@mdi/js';

// Import required utility file
import { numberWithCommas } from 'helper/utils';

const GKInstructorCard = ({ item }) => {
	return (
    <Card className="mb-4 card-hover">
      {/* img */}
      <Card.Img
        variant="top"
        src={item.image}
        className="rounded-top-md img-fluid"
      />
      {/* card body */}
      <Card.Body>
        <span className="text-primary mb-3 d-block text-uppercase fw-semi-bold ls-xl">
          Step {item.id}
        </span>
        <h3 className="mb-0 fw-semi-bold">
          <Link href={item.link} className="text-inherit">
            {item.name}
          </Link>
        </h3>
        <p className="mb-3">{item.designation}</p>
      </Card.Body>
    </Card>
  );
};

// Typechecking With PropTypes
GKInstructorCard.propTypes = {
	item: PropTypes.object.isRequired
};

export default GKInstructorCard;
