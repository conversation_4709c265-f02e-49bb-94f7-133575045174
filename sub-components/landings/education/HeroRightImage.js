// import node module libraries
import { Col, Row, Container, ListGroup, Image } from 'react-bootstrap';
import Link from 'next/link';

// import bootstrap icons
import { CheckCircleFill } from 'react-bootstrap-icons';

const HeroRightImage = () => {
  return (
    <section className="py-lg-16 py-6 bg-white">
      <Container>
        <Row className="d-flex align-items-center">
          <Col
            xxl={{ offset: 1, span: 4 }}
            xl={4}
            lg={4}
            xs={12}
            className="d-lg-flex justify-content-end">
            <div className="mt-10 mt-lg-0 position-relative">
              {/* <div className="position-absolute top-0 start-0 translate-middle  d-none d-md-block">
                <Image src="/images/svg/graphics-2.svg" alt="graphics-2" />
              </div> */}
              <Image
                src="/cat.png"
                alt="online course"
                className="img-fluid rounded-3 w-100 z-1 position-relative"
              />
              {/* <div className="position-absolute top-100 start-100 translate-middle  d-none d-md-block">
                <Image src="/images/svg/graphics-1.svg" alt="graphics-1" />
              </div> */}
            </div>
          </Col>
          <Col xxl={{ span: 6 }} xl={8} lg={8} xs={12}>
            <div>
              <h1 className="display-3 fw-bold mb-3">
                From Idea to Reality: Empowering Startups for Success
                {/* <u className="text-warning">
                  <span className="text-primary"> Startups</span>
                </u> */}
              </h1>
              <p className="lead mb-4">
                Sharif empowers innovative startups and visionary entrepreneurs
                through strategic investments and expert guidance, driving
                transformative business success.
              </p>
              {/* <ListGroup as="ul" bsPrefix="list-unstyled" className="mb-5">
                <ListGroup.Item as="li" bsPrefix="mb-2">
                  <CheckCircleFill size={20} fill="var(--geeks-success)" />
                  <span className="lead ms-2 mt-2">Virtual CFO Services</span>
                </ListGroup.Item>
                <ListGroup.Item as="li" bsPrefix="mb-2">
                  <CheckCircleFill size={20} fill="var(--geeks-success)" />
                  <span className="lead ms-2 mt-2">
                    Strategic Financial Planning
                  </span>
                </ListGroup.Item>
                <ListGroup.Item as="li" bsPrefix="mb-2">
                  <CheckCircleFill size={20} fill="var(--geeks-success)" />
                  <span className="lead ms-2 mt-2">Cash Flow Management</span>
                </ListGroup.Item>
                <ListGroup.Item as="li" bsPrefix="mb-2">
                  <CheckCircleFill size={20} fill="var(--geeks-success)" />
                  <span className="lead ms-2 mt-2">
                    Financial Reporting & Analysis
                  </span>
                </ListGroup.Item>
                <ListGroup.Item as="li" bsPrefix="">
                  <CheckCircleFill size={20} fill="var(--geeks-success)" />
                  <span className="lead ms-2 mt-2">
                    Fundraising & Investor Support
                  </span>
                </ListGroup.Item>
              </ListGroup> */}
              {/* <Link href="/platforms" className="btn btn-dark btn-lg">
                View Platforms
              </Link> */}
              <Link href="/register" className="btn btn-dark btn-lg">
                Start Investing
              </Link>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
}

export default HeroRightImage