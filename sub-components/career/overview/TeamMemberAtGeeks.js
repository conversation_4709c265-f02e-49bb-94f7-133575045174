// import node module libraries
import { Col, <PERSON>, Container, Image } from 'react-bootstrap';

// import widget/custom components
import { SectionHeadingLeft3 }  from 'widgets';

const TeamMemberAtGeeks = () => {
	const title = "Our People are Our Greatest Asset";
	const subtitle = 'Founders Note';
	const description = `At Sharif, we recognize that our employees are irreplaceable assets, bringing unique abilities, knowledge, and experience that drive our success.`;

	return (
    <section className="pt-lg-14 pb-lg-18 pb-8 bg-white">
      <Container>
        <Row>
          <Col xl={{ offset: 1, span: 10 }} xs={12}>
            <SectionHeadingLeft3
              title={title}
              description={description}
              subtitle={subtitle}
              className="display-4"
            />

            <div className="mt-8">
              <Row className="align-items-center">
                <Col lg={6} md={12} sm={12}>
                  <div>
                    <p className="h2 fw-normal mb-6 lh-lg">
                      "At Sharif, we believe in mutual trust and maintaining clarity and transparency, just like water."
                    </p>
                    <h3 className="mb-1"><PERSON><PERSON></h3>
                    <p className="mb-0">Founder</p>
                  </div>
                </Col>
                <Col lg={{ offset: 1, span: 5 }} md={12} sm={12}>
                  <div className="mt-6 mt-lg-0">
                    <Image
                      src="/images/user/founder.png"
                      alt=""
                      className="img-fluid w-100 rounded-3"
                    />
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default TeamMemberAtGeeks;
