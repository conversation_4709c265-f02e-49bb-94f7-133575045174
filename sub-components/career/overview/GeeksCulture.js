// import node module libraries
import { Col, Row, Container } from 'react-bootstrap';
import Link from 'next/link';

// import widget/custom components
import { SectionHeadingLeft3 }  from 'widgets';


const GeeksCulture = () => {
	const title = "Driving Business Success";
	const subtitle = 'Sharif Culture';
	const description = `Access to leadership training, success guides, anti-bias & harassment training, and executive coaching.`;

	return (
    <section className="py-14 bg-light">
      <Container>
        <Row>
          <Col xl={{ offset: 1, span: 10 }} xs={12}>
            <SectionHeadingLeft3
              title={title}
              description={description}
              subtitle={subtitle}
              className="display-4"
            />

            <div className="mt-8">
              <Row>
                <Col lg={6} md={6} xs={12}>
                  <div className="mb-6 pe-xl-12">
                    <h3 className="mb-3 fw-bold">Trust and Pride</h3>
                    <p className="mb-0 fs-4 ">
                      We foster a workplace where trust, pride in work, and
                      camaraderie are paramount.
                    </p>
                  </div>
                </Col>
                <Col lg={6} md={6} xs={12}>
                  <div className="mb-6 pe-xl-12">
                    <h3 className="mb-3 fw-bold">Core Values</h3>
                    <p className="mb-0 fs-4 ">
                      Our values shape how we work together, ensuring we bring
                      our best to achieve our mission.
                    </p>
                  </div>
                </Col>
                <Col lg={6} md={6} xs={12}>
                  <div className="mb-6 pe-xl-12">
                    <h3 className="mb-3 fw-bold">Empowering Growth</h3>
                    <p className="mb-0 fs-4 ">
                      Take on significant responsibilities that challenge you
                      and drive your professional growth.
                    </p>
                  </div>
                </Col>
                <Col lg={6} md={6} xs={12}>
                  <div className="mb-6 pe-xl-12">
                    <h3 className="mb-3 fw-bold">Comprehensive Benefits</h3>
                    <p className="mb-0  fs-4 ">
                      Our benefits are designed to keep you energized, focused,
                      and thriving, with extensive mental health support.
                    </p>
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default GeeksCulture;
