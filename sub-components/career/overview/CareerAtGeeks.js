// import node module libraries
import { Row, Col, Container } from 'react-bootstrap';

// import widget/custom components
import { SectionHeadingLeft3, StatTopLine }  from 'widgets';

const CareerAtGeeks = () => {
	const title = "Empowering Startups, Simplifying Funding";
	const subtitle = 'About Sharif';
	const description = `We’re creating a new standard in venture capital, driven by transparency, trust, and technology that fuels positive change in the world.`;
	const btntext = 'See all open positions';
	const btnlink = '/marketing/pages/career-list/';

	return (
		<section className="py-14 bg-white">
			<Container>
				<Row>
					<Col xl={{ span: 10, offset: 1 }} sm={12}>
						{/* section heading */}

						<SectionHeadingLeft3
							title={title}
							description={description}
							subtitle={subtitle}
							btntext={btntext}
							btnlink={btnlink}
						/>

						{/* stat counters */}

						{/* <Row>
							<Col lg={4} md={4} xs={12}>
								<StatTopLine title="Team members" value="100+" />
							</Col>
							<Col lg={4} md={4} xs={12}>
								<StatTopLine title="in VC funding" value="$47M" />
							</Col>
							<Col lg={4} md={4} xs={12}>
								<StatTopLine title="Customers" value="18K" />
							</Col>
						</Row> */}
					</Col>
				</Row>
			</Container>
		</section>
	);
};
export default CareerAtGeeks;
