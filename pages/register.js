// import node module libraries
import { Fragment, useEffect } from 'react';
import { Col, Container, Row } from 'react-bootstrap';

// import sub components
import { HeroGradient, SASSFeatures4Columns, AppIntegration, CustomersTestimonials, SimplePricingPlans, HeroLeftImage, HeroRightImage, CoursesFeatures4Columns, WorldClassInstructors } from 'sub-components';

// import widget/custom components
import { CTALightBG, GeeksSEO } from 'widgets';

// import layouts
import NavbarLanding from 'layouts/marketing/navbars/NavbarLanding';
import FooterCenter from 'layouts/marketing/footers/FooterWithLinks';

// import your layout to override default layout 
import BlankLayout from 'layouts/marketing/BlankLayout';
import HowStepOne from 'sub-components/landings/landing-sass/HowStepOne';
import HowStepTwo from 'sub-components/landings/landing-sass/HowStepTwo';
import HowStepThree from 'sub-components/landings/landing-sass/HowStepThree';
import HowStepFour from 'sub-components/landings/landing-sass/HowStepFour';
import HowStepFive from 'sub-components/landings/landing-sass/HowStepFive';
import HowStepSix from 'sub-components/landings/landing-sass/HowStepSix';

const LandingSass = () => {
	useEffect(() => {
		document.body.className = 'bg-white';
	});

	const title = 'Just try it out! You’ll fall in love';
	const subtitle = 'Get things done';
	const description = `Designed for modern companies looking to launch a simple, premium and modern website and apps.`;
	const btntext = 'Try For Free';
	const btnlink = '#';

	return (
    <Fragment>
      {/* Geeks SEO settings  */}
      <GeeksSEO title="Register as Investor | Sharif.llc  " />

      <main>
        {/* hero gradient */}
        <section className="pt-14 pb-9 bg-white">
          <Container>
            <Row>
              <Col
                lg={{ span: 10, offset: 1 }}
                xl={{ span: 8, offset: 2 }}
                md={12}
                sm={12}>
                <div className="text-center mb-5">
                  <h1 className=" display-2 fw-bold ">Register As Investor</h1>
                  <p className="lead ">
                    Simplifying investments with clear insights and easy
                    onboarding.
                  </p>
                  {/* Form */}
                  {/* <Link href="/invest">
                        <Button variant="primary" type="submit">
                          Buy Portfolio Shares
                        </Button>
                      </Link> */}
                </div>
              </Col>
            </Row>
          </Container>
        </section>

        {/*  World Class Instructors Section  */}
        <WorldClassInstructors />

        {/* Why learn with geeks */}
        <CoursesFeatures4Columns />
      </main>
    </Fragment>
  );
};


export default LandingSass;
