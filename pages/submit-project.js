// import node module libraries
import { Fragment } from 'react';
import Link from 'next/link';
import { Col, Row } from 'react-bootstrap';

// import widget/custom components
import { GeeksSEO } from 'widgets';

// import sub components
import { ContactForm } from 'sub-components';

// import your layout to override default layout 
import DefaultLayout from 'layouts/marketing/DefaultLayout';
import Image from 'next/image';
import { EnvelopeOpen, GeoAlt, Telephone } from 'react-bootstrap-icons';

const Contact = () => {
	return (
    <Fragment>
      {/* Geeks SEO settings  */}
      <GeeksSEO title="Contact | Sharif" />
      <main>
        <section className="bg-white container-fluid">
          <Row className="align-items-center justify-content-center min-vh-100">
            <Col lg={5} md={12} sm={12}>
              <div className="px-xl-10 px-md-8 px-4 py-8 py-lg-0">
                <div className="text-dark">
                  <p className="lead text-dark">
                    Welcome! Thank you for your interest in partnering with
                    Sharif portfolio startups.
                  </p>

                  <p className="lead text-dark">
                    Please complete the project submission form, and our team
                    will reach out to review the details and schedule a meeting.
                  </p>

                  <p className="lead text-dark">
                    We look forward to exploring how your project can align with
                    our portfolio and drive future innovation and success.
                  </p>
                </div>
              </div>
            </Col>

            {/* right side form section with background color */}
            <Col
              lg={5}
              className="d-lg-flex align-items-center w-lg-50 min-vh-lg-100 bg-cover top-0 right-0">
              {/* <ContactForm /> */}
              <iframe
                style={{ width: "100%", height: 720 }}
                id="investor-relations-41kvk1"
                src="https://noteforms.com/forms/investor-relations-41kvk1"></iframe>
            </Col>
          </Row>
        </section>
      </main>
    </Fragment>
  );
};

Contact.Layout = DefaultLayout;

export default Contact;
