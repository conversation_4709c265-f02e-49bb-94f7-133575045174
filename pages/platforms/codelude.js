// import node module libraries
import { useEffect } from 'react'
import { Col, Row, Container, Image } from 'react-bootstrap';
import Link from 'next/link';

// import bootstrap icons
import { ArrowUpRight, ChatQuoteFill } from 'react-bootstrap-icons';
import { CTAButton } from 'widgets';

const PortfolioSingle = () => {
   const title = "Join the Sharif Team & Shape the Future";
   const description = `If you’re passionate about innovation and ready to make an impact, we’d love to meet you. We’re dedicated to supporting your professional development and overall well-being.`;
   const btntext = "Make Investment";
   const btnlink = "#";
    useEffect(() => {
        document.body.className = 'bg-white';
    });

    return (
      <main>
        <section className="py-lg-12 py-7 bg-white">
          <Container>
            <Row>
              <Col md={{ offset: 1, span: 10 }} xs={12}>
                <div className="mb-10">
                  <Row>
                    <Col md={10} xs={12}>
                      <div className="mb-8">
                        <h1 className="display-3 fw-bold mb-4">Codelude</h1>
                        <p className="lead mb-0 pe-lg-8">
                          Codelude delivers expert development, creative
                          content, and strategic marketing to empower businesses
                          in the digital landscape.
                        </p>
                      </div>
                    </Col>
                    <Col xs={12}>
                      <Image
                        src={"/images/startup/codelude/1.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div className="mb-6">
                        <h3 className="mb-4">Services</h3>
                        <ul className="list-unstyled fs-4">
                          <li className="mb-2">Application Development</li>
                          <li className="mb-2">Content Creation</li>
                          <li className="mb-2">Digital Marketing</li>{" "}
                        </ul>
                      </div>
                      <div>
                        <h3 className="mb-3">Website</h3>
                        <Link
                          href="https://codelude.com/"
                          className="fs-4 text-inherit">
                          https://codelude.com/
                        </Link>
                      </div>
                    </Col>
                    <Col md={9} xs={12}>
                      <div className="mb-6 mb-lg-0">
                        <h2 className="mb-4">Project Summary</h2>
                        <p className="fs-4 mb-4">
                          Codelude focuses on turning ideas into reality by
                          providing top-notch application development, engaging
                          content creation, and effective digital marketing. Our
                          services are designed to enhance digital presence and
                          drive growth, making us a one-stop shop for all
                          digital needs.
                        </p>
                        <p className="fs-4 mb-0">
                          <h4>Current Focus:</h4>
                          <li>
                            Building robust, scalable applications across
                            multiple platforms, from iOS, Android and web.
                          </li>
                          <li>
                            Creating captivating user experiences with
                            high-quality UI/UX design, writing, and multimedia
                            content.
                          </li>
                          <li>
                            Developing targeted marketing strategies to boost
                            visibility and customer engagement.
                          </li>
                        </p>
                      </div>
                    </Col>
                  </Row>
                </div>
                <hr className="mb-12" />
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">The challenge</h2>
                      </div>
                    </Col>
                    <Col md={9} xs={12}>
                      <p className="fs-4 mb-0">
                        Codelude faced the challenge of offering a diverse range
                        of services while maintaining quality and innovation.
                        The goal was to balance technical development with
                        creative content and strategic marketing to meet diverse
                        client needs.
                      </p>
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/codelude/2.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/codelude/3.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">Solution</h2>
                      </div>
                    </Col>
                    <Col md={9} xs={12}>
                      <p className="fs-4 mb-0">
                        Codelude leveraged a multidisciplinary approach,
                        integrating application development with creative
                        content services and strategic marketing. This approach
                        ensured consistency and quality across all services,
                        catering to a broad spectrum of client requirements.
                      </p>
                    </Col>
                    <Col md={12} className="mt-6">
                      <Image
                        src={"/images/startup/codelude/4.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={7} xs={12}>
                      <div className="mb-6 mb-md-0">
                        <h2 className="mb-4">Results</h2>
                        <p className="fs-4 mb-0">
                          Codelude has successfully built a reputation for
                          delivering high-quality digital solutions, expanding
                          its client base, and achieving substantial growth
                          across all service areas, from application development
                          to content creation and marketing.
                        </p>
                      </div>
                    </Col>
                    <Col md={5} xs={12}>
                      <Row>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Valuation</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Annual Sales</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Customers</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">0%</h2>
                            <span className="fs-4">Annual Growth</span>
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </div>
                <hr className="mb-8" />
                <Row className="">
                  <Col xs={12}>
                    <h2 className="h1 mb-8">Related Portfolio</h2>
                  </Col>
                  <Col md={6}>
                    <div className="mb-6">
                      <div className="img-overlay">
                        <div className="img-color">
                          <Link href="/startup/cuestay">
                            <Image
                              src={"/images/startup/cuestay/1.png"}
                              alt="portfolio"
                              className="img-fluid w-100"
                            />
                          </Link>
                          <div className="caption">
                            <Link
                              href="/startup/cuestay"
                              className="btn btn-white">
                              View Details
                            </Link>
                          </div>
                        </div>
                      </div>
                      <div className="mt-4">
                        <div className="d-flex justify-content-between align-items-center">
                          <h3 className="fw-semibold mb-1">
                            <Link
                              href="/startup/cuestay"
                              className="text-inherit">
                              Cuestay
                            </Link>
                          </h3>
                          <Link href="/startup/cuestay">
                            <ArrowUpRight size={14} fill="currentColor" />
                          </Link>
                        </div>
                        <span>Retail - Home Accessories</span>
                      </div>
                    </div>
                  </Col>
                  <Col md={6}>
                    <div className="mb-6">
                      <div className="img-overlay">
                        <div className="img-color">
                          <Link href="/startup/chefless">
                            <Image
                              src={"/images/startup/chefless/1.jpg"}
                              alt="portfolio"
                              className="img-fluid w-100"
                            />
                          </Link>
                          <div className="caption">
                            <Link
                              href="/startup/chefless"
                              className="btn btn-white">
                              View Details
                            </Link>
                          </div>
                        </div>
                      </div>
                      <div className="mt-4">
                        <div className="d-flex justify-content-between align-items-center">
                          <h3 className="fw-semibold mb-1">
                            <Link
                              href="/startup/chefless"
                              className="text-inherit">
                              Chefless
                            </Link>
                          </h3>
                          <Link href="/startup/chefless">
                            <ArrowUpRight size={14} fill="currentColor" />
                          </Link>
                        </div>
                        <span>Food - Ready To Cook</span>
                      </div>
                    </div>
                  </Col>
                </Row>
              </Col>
            </Row>
          </Container>
        </section>
        <CTAButton
          title={title}
          description={description}
          btntext={btntext}
          btnlink={btnlink}
        />
      </main>
    );
}

export default PortfolioSingle