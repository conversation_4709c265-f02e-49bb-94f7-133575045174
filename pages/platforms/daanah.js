// import node module libraries
import { useEffect } from 'react'
import { Col, Row, Container, Image } from 'react-bootstrap';
import Link from 'next/link';

// import bootstrap icons
import { ArrowUpRight, ChatQuoteFill } from 'react-bootstrap-icons';

const PortfolioSingle = () => {
    useEffect(() => {
        document.body.className = 'bg-white';
    });

    return (
      <main>
        <section className="py-lg-12 py-7 bg-white">
          <Container>
            <Row>
              <Col md={{ offset: 1, span: 10 }} xs={12}>
                <div className="mb-10">
                  <Row>
                    <Col md={10} xs={12}>
                      <div className="mb-8">
                        <h1 className="display-3 fw-bold mb-4">Daanah</h1>
                        <p className="lead mb-0 pe-lg-8">
                          <PERSON><PERSON> successfully developed and managed multiple
                          franchise projects, utilizing their extensive
                          expertise in venture capital and franchise management.
                          They raised significant funds, ensuring each project’s
                          growth and success.
                        </p>
                      </div>
                    </Col>
                    <Col xs={12}>
                      <Image
                        src={"/images/startup/daanah/1.png"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={8} xs={12}>
                      <div className="mb-6 mb-lg-0">
                        <h2 className="mb-4">Project Summary</h2>
                        <p className="fs-4 mb-4">
                          Sharifs introduced a cutting-edge platform tailored
                          for franchise management and crowdfunding. This
                          platform enabled streamlined operations, improved
                          communication with shareholders, and efficient
                          fund-raising for new and existing franchise projects.
                          Their approach involved integrating sophisticated
                          financial tools and user-friendly interfaces to
                          enhance the overall management process.
                        </p>
                        <p className="fs-4 mb-0">
                          The implementation of this solution led to: Efficient
                          management of franchise operations. Enhanced
                          communication and transparency with shareholders.
                          Successful fundraising efforts to support project
                          development and expansion.
                        </p>
                      </div>
                    </Col>
                    <Col md={{ offset: 1, span: 3 }} xs={12}>
                      <div className="mb-6">
                        <h3 className="mb-4">Products</h3>
                        <ul className="list-unstyled fs-4">
                          <li className="mb-2">Abaya</li>
                          <li className="mb-2">Jalabiya</li>
                          <li className="mb-2">Kuftan</li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="mb-3">Website</h3>
                        <Link
                          href="https://daanah.com/"
                          className="fs-4 text-inherit">
                          https://daanah.com/
                        </Link>
                      </div>
                    </Col>
                  </Row>
                </div>
                <hr className="mb-12" />
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">The challenge</h2>
                      </div>
                    </Col>
                    <Col md={{ offset: 1, span: 8 }} xs={12}>
                      <p className="fs-4 mb-0">
                        Sharifs faced the challenge of managing complex
                        franchise operations and raising sufficient funds to
                        support ambitious projects. They needed a seamless
                        platform to streamline these processes and engage
                        effectively with shareholders.
                      </p>
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/daanah/2.png"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/daanah/3.png"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">Solution</h2>
                      </div>
                    </Col>
                    <Col md={{ offset: 1, span: 8 }} xs={12}>
                      <p className="fs-4 mb-0">
                        Sharifs implemented an advanced platform for franchise
                        management and crowdfunding. This solution allowed them
                        to efficiently handle franchise operations, communicate
                        transparently with shareholders, and secure the
                        necessary funding for project development. By
                        integrating sophisticated financial tools and
                        user-friendly interfaces, Sharifs ensured the growth and
                        success of each franchise initiative.
                      </p>
                    </Col>
                    <Col md={12} className="mt-6">
                      <Image
                        src={"/images/startup/daanah/4.png"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={7} xs={12}>
                      <div className="mb-6 mb-md-0">
                        <h2 className="mb-4">Results</h2>
                        <p className="fs-4 mb-0">
                          Vivamus at odio a neque fermentum tincidunt ac id
                          neque. Aenean convallis mi massa, ac volutpat leo
                          fringilla in. Curabitur malesuada sit amet nulla at
                          efficitur.{" "}
                        </p>
                      </div>
                    </Col>
                    <Col md={5} xs={12}>
                      <Row>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">10M</h2>
                            <span className="fs-4">Valuation</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">187B</h2>
                            <span className="fs-4">Annual Sales</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">21K</h2>
                            <span className="fs-4">Customers</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">85%</h2>
                            <span className="fs-4">Annual Growth</span>
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                  <hr className="my-8" />
                  <Row className="">
                    <Col xs={12}>
                      <h2 className="h1 mb-8">Related Portfolio</h2>
                    </Col>
                    <Col md={6}>
                      <div className="mb-6">
                        <div className="img-overlay">
                          <div className="img-color">
                            <Link href="/startup/sxe">
                              <Image
                                src={"/images/startup/sxe/1.png"}
                                alt="portfolio"
                                className="img-fluid w-100"
                              />
                            </Link>
                            <div className="caption">
                              <Link
                                href="/startup/sxe"
                                className="btn btn-white">
                                View Details
                              </Link>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="d-flex justify-content-between align-items-center">
                            <h3 className="fw-semibold mb-1">
                              <Link
                                href="/startup/sxe"
                                className="text-inherit">
                                SXE
                              </Link>
                            </h3>
                            <Link href="/startup/sxe">
                              <ArrowUpRight size={14} fill="currentColor" />
                            </Link>
                          </div>
                          <span>Fashion - Designer</span>
                        </div>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-6">
                        <div className="img-overlay">
                          <div className="img-color">
                            <Link href="/startup/dietized">
                              <Image
                                src={"/images/startup/dietized/1.jpg"}
                                alt="portfolio"
                                className="img-fluid w-100"
                              />
                            </Link>
                            <div className="caption">
                              <Link
                                href="/startup/dietized"
                                className="btn btn-white">
                                View Details
                              </Link>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="d-flex justify-content-between align-items-center">
                            <h3 className="fw-semibold mb-1">
                              <Link
                                href="/startup/dietized"
                                className="text-inherit">
                                Dietized
                              </Link>
                            </h3>
                            <Link href="/startup/dietized">
                              <ArrowUpRight size={14} fill="currentColor" />
                            </Link>
                          </div>
                          <span>Food - Health Kitchen</span>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Row>
          </Container>
        </section>
      </main>
    );
}

export default PortfolioSingle