// import node module libraries
import { useEffect } from 'react'
import { Col, Row, Container, Image } from 'react-bootstrap';
import Link from 'next/link';

// import bootstrap icons
import { ArrowUpRight, ChatQuoteFill } from 'react-bootstrap-icons';
import { CTAButton } from 'widgets';

const PortfolioSingle = () => {
    useEffect(() => {
        document.body.className = 'bg-white';
    });

    const title = "Join the Sharif Team & Shape the Future";
    const description = `If you’re passionate about innovation and ready to make an impact, we’d love to meet you. We’re dedicated to supporting your professional development and overall well-being.`;
    const btntext = "Make Investment";
    const btnlink = "#";

    return (
      <main>
        <section className="py-lg-12 py-7 bg-white">
          <Container>
            <Row>
              <Col md={{ offset: 1, span: 10 }} xs={12}>
                <div className="mb-10">
                  <Row>
                    <Col md={10} xs={12}>
                      <div className="mb-8">
                        <h1 className="display-3 fw-bold mb-4">Franchiseen</h1>
                        <p className="lead mb-0 pe-lg-8">
                          Franchiseen is a comprehensive fintech platform
                          designed for franchise businesses, offering powerful
                          tools for managing franchise financials, networking,
                          and investments through a mix of static data and
                          dynamic updates.
                        </p>
                      </div>
                    </Col>
                    <Col xs={12}>
                      <Image
                        src={"/images/startup/franchiseen/1.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={8} xs={12}>
                      <div className="mb-6 mb-lg-0">
                        <h2 className="mb-4">Project Summary</h2>
                        <p className="fs-4 mb-4">
                          Franchiseen is revolutionizing the franchise industry
                          by providing a platform where franchisers and
                          franchisees can dynamically manage and monitor their
                          financial operations. With features like franchise
                          crowdfunding, financial dashboards, and an integrated
                          networking system, Franchiseen offers a robust fintech
                          solution for all franchise stakeholders.
                        </p>
                        <p className="fs-4 mb-0">
                          <h4>Current Focus:</h4>
                          <li>
                            Improving financial tools with real-time updates.
                          </li>
                          <li>
                            Expanding the user base across various franchise
                            sizes.
                          </li>
                          <li>
                            Strengthening communication between franchisees and
                            franchisers.
                          </li>
                        </p>
                        {/* <p className="fs-4 mb-0">
                          The implementation of this solution led to: Efficient
                          management of franchise operations. Enhanced
                          communication and transparency with shareholders.
                          Successful fundraising efforts to support project
                          development and expansion.
                        </p> */}
                      </div>
                    </Col>
                    <Col md={{ offset: 1, span: 3 }} xs={12}>
                      <div className="mb-6">
                        <h3 className="mb-4">Services</h3>
                        <ul className="list-unstyled fs-4">
                          <li className="mb-2">Crowdfunding & Share Trading</li>
                          <li className="mb-2">
                            Franchise Financial Management
                          </li>
                          <li className="mb-2">
                            Franchisee-Franchiser Networking
                          </li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="mb-3">Website</h3>
                        <Link
                          href="https://franchiseen.com/"
                          className="fs-4 text-inherit">
                          https://franchiseen.com/
                        </Link>
                      </div>
                    </Col>
                  </Row>
                </div>
                <hr className="mb-12" />
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">The challenge</h2>
                      </div>
                    </Col>
                    <Col md={{ offset: 1, span: 8 }} xs={12}>
                      <p className="fs-4 mb-0">
                        Franchiseen needed to create a platform that balanced
                        franchisers’ need for control over franchise operations
                        (such as finances and communications) with the dynamic
                        management of franchisee investments and crowdfunding
                        initiatives. Additionally, the platform required
                        building a secure network for managing financial
                        transactions and fostering engagement between
                        franchisees and franchisers.
                      </p>
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/franchiseen/2.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/franchiseen/3.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">Solution</h2>
                      </div>
                    </Col>
                    <Col md={{ offset: 1, span: 8 }} xs={12}>
                      <p className="fs-4 mb-0">
                        Franchiseen developed a fintech platform that integrates
                        static franchise data with dynamic, automated updates
                        from connected POS, camera, and financial systems. The
                        platform also enables franchisers to crowdfund new
                        franchise projects, sell shares, and manage financial
                        transactions securely. Networking features allow
                        franchisees and franchisers to engage effectively within
                        the ecosystem.
                      </p>
                    </Col>
                    <Col md={12} className="mt-6">
                      <Image
                        src={"/images/startup/franchiseen/4.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={7} xs={12}>
                      <div className="mb-6 mb-md-0">
                        <h2 className="mb-4">Results</h2>
                        <p className="fs-4 mb-0">
                          Franchiseen has successfully attracted a growing user
                          base of franchise businesses, providing a modern
                          approach to franchise financial management and
                          operations. It has become a preferred platform for
                          franchisers looking to scale their business and for
                          franchisees to invest, track, and manage their
                          franchise opportunities dynamically.
                        </p>
                      </div>
                    </Col>
                    <Col md={5} xs={12}>
                      <Row>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Valuation</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Annual Sales</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Customers</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">0%</h2>
                            <span className="fs-4">Annual Growth</span>
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                  <hr className="my-8" />
                  <Row className="">
                    <Col xs={12}>
                      <h2 className="h1 mb-8">Related Portfolio</h2>
                    </Col>
                    <Col md={6}>
                      <div className="mb-6">
                        <div className="img-overlay">
                          <div className="img-color">
                            <Link href="/startup/chefless">
                              <Image
                                src={"/images/startup/chefless/1.jpg"}
                                alt="portfolio"
                                className="img-fluid w-100"
                              />
                            </Link>
                            <div className="caption">
                              <Link
                                href="/startup/chefless"
                                className="btn btn-white">
                                View Details
                              </Link>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="d-flex justify-content-between align-items-center">
                            <h3 className="fw-semibold mb-1">
                              <Link
                                href="/startup/chefless"
                                className="text-inherit">
                                Chefless
                              </Link>
                            </h3>
                            <Link href="/startup/chefless">
                              <ArrowUpRight size={14} fill="currentColor" />
                            </Link>
                          </div>
                          <span>Food - Ready To Cook</span>
                        </div>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-6">
                        <div className="img-overlay">
                          <div className="img-color">
                            <Link href="/startup/cuestay">
                              <Image
                                src={"/images/startup/cuestay/1.png"}
                                alt="portfolio"
                                className="img-fluid w-100"
                              />
                            </Link>
                            <div className="caption">
                              <Link
                                href="/startup/codelude"
                                className="btn btn-white">
                                View Details
                              </Link>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="d-flex justify-content-between align-items-center">
                            <h3 className="fw-semibold mb-1">
                              <Link
                                href="/startup/codelude"
                                className="text-inherit">
                                Cuestay
                              </Link>
                            </h3>
                            <Link href="/startup/codelude">
                              <ArrowUpRight size={14} fill="currentColor" />
                            </Link>
                          </div>
                          <span>Retail - Home Accesories</span>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Row>
          </Container>
        </section>
        <CTAButton
          title={title}
          description={description}
          btntext={btntext}
          btnlink={btnlink}
        />
      </main>
    );
}

export default PortfolioSingle