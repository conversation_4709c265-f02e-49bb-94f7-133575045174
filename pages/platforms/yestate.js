// import node module libraries
import { useEffect } from 'react'
import { Col, Row, Container, Image } from 'react-bootstrap';
import Link from 'next/link';

// import bootstrap icons
import { ArrowUpRight, ChatQuoteFill } from 'react-bootstrap-icons';
import { CTAButton } from 'widgets';

const PortfolioSingle = () => {
   const title = "Join the Sharif Team & Shape the Future";
   const description = `If you’re passionate about innovation and ready to make an impact, we’d love to meet you. We’re dedicated to supporting your professional development and overall well-being.`;
   const btntext = "Make Investment";
   const btnlink = "#";
    useEffect(() => {
        document.body.className = 'bg-white';
    });

    return (
      <main>
        <section className="py-lg-12 py-7 bg-white">
          <Container>
            <Row>
              <Col md={{ offset: 1, span: 10 }} xs={12}>
                <div className="mb-10">
                  <Row>
                    <Col md={10} xs={12}>
                      <div className="mb-8">
                        <h1 className="display-3 fw-bold mb-4">Yestate</h1>
                        <p className="lead mb-0 pe-lg-8">
                          Yestate is a real estate services company specializing
                          in procuring, managing, and facilitating transactions
                          for commercial properties. Through our integrated
                          platform and strategic consulting services, we help
                          businesses find the ideal commercial spaces for
                          retail, offices, warehouses, and more. With a focus on
                          value-driven solutions and a robust service network,
                          Yestate aims to simplify commercial real estate for
                          clients while expanding its presence across major
                          business hubs.
                        </p>
                      </div>
                    </Col>
                    <Col xs={12}>
                      <Image
                        src={"/images/startup/yestate/1.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={8} xs={12}>
                      <div className="mb-6 mb-lg-0">
                        <h2 className="mb-4">Project Summary</h2>
                        <p className="fs-4 mb-4">
                          Yestate is a dynamic company that focuses on providing
                          expert services in the commercial real estate sector.
                          We specialize in identifying, acquiring, and managing
                          high-value commercial properties for clients ranging
                          from small businesses to large enterprises. Our
                          end-to-end service model ensures a seamless experience
                          — from property discovery to leasing or acquisition.
                        </p>
                        {/* <p className="fs-4 mb-0">
                          The implementation of this solution led to: Efficient
                          management of franchise operations. Enhanced
                          communication and transparency with shareholders.
                          Successful fundraising efforts to support project
                          development and expansion.
                        </p> */}
                        <p className="fs-4 mb-0">
                          <h4>Current Focus:</h4>
                          <li>
                            Expanding our portfolio of commercial properties in
                            high-growth areas.
                          </li>
                          <li>
                            Strengthening our service delivery with data-driven
                            property insights.
                          </li>
                          <li>
                            Offering personalized consulting for leasing,
                            buying, and investment in commercial real estate.
                          </li>
                        </p>
                      </div>
                    </Col>
                    <Col md={{ offset: 1, span: 3 }} xs={12}>
                      <div className="mb-6">
                        <h2 className="mb-4">Services</h2>
                        <ul className="list-unstyled fs-4">
                          <li className="mb-2">Commercial Property</li>
                          <li className="mb-2">Real Estate Portfolio</li>
                          <li className="mb-2">Tenant Management</li>
                          <li className="mb-2">
                            Market Research & Site Selection
                          </li>
                        </ul>
                      </div>
                      <div>
                        <h2 className="mb-3">Website</h2>
                        <Link
                          href="https://yestate.co/"
                          className="fs-4 text-inherit">
                          https://yestate.co/
                        </Link>
                      </div>
                    </Col>
                  </Row>
                </div>
                <hr className="mb-12" />
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">The challenge</h2>
                      </div>
                    </Col>
                    <Col md={{ offset: 1, span: 8 }} xs={12}>
                      <p className="fs-4 mb-0">
                        Yestate operates in a highly competitive commercial real
                        estate market, where the challenge is to deliver
                        value-driven solutions while maintaining speed and
                        accuracy. Building trust, differentiating our service
                        offerings, and establishing a strong presence in
                        emerging markets are key to driving sustainable growth.
                      </p>
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/yestate/2.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/yestate/3.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">Solution</h2>
                      </div>
                    </Col>
                    <Col md={{ offset: 1, span: 8 }} xs={12}>
                      <p className="fs-4 mb-0">
                        Yestate bridges the gap between businesses and
                        high-quality commercial spaces. Our team provides
                        curated property options based on market data and
                        business goals, ensuring each client makes informed,
                        strategic decisions. We are currently seeking
                        investments and strategic partnerships to scale
                        operations, enhance our platform, and expand into new
                        commercial zones.
                      </p>
                    </Col>
                    <Col md={12} className="mt-6">
                      <Image
                        src={"/images/startup/yestate/4.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={7} xs={12}>
                      <div className="mb-6 mb-md-0">
                        <h2 className="mb-4">Results</h2>
                        <p className="fs-4 mb-0">
                          Yestate is in the growth phase of building a strong
                          footprint in the commercial real estate industry. We
                          are focused on delivering tailored property solutions,
                          expanding our portfolio, and enhancing client
                          experiences. Our emphasis on transparency, insight,
                          and long-term value positions us to become a leader in
                          the commercial real estate space.
                        </p>
                      </div>
                    </Col>
                    <Col md={5} xs={12}>
                      <Row>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Valuation</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Annual Sales</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Customers</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">0%</h2>
                            <span className="fs-4">Annual Growth</span>
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                  <hr className="my-8" />
                  <Row className="">
                    <Col xs={12}>
                      <h2 className="h1 mb-8">Related Portfolio</h2>
                    </Col>

                    <Col md={6}>
                      <div className="mb-6">
                        <div className="img-overlay">
                          <div className="img-color">
                            <Link href="/startup/chefless">
                              <Image
                                src={"/images/startup/chefless/1.jpg"}
                                alt="portfolio"
                                className="img-fluid w-100"
                              />
                            </Link>
                            <div className="caption">
                              <Link
                                href="/startup/chefless"
                                className="btn btn-white">
                                View Details
                              </Link>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="d-flex justify-content-between align-items-center">
                            <h3 className="fw-semibold mb-1">
                              <Link
                                href="/startup/chefless"
                                className="text-inherit">
                                Chefless
                              </Link>
                            </h3>
                            <Link href="/startup/chefless">
                              <ArrowUpRight size={14} fill="currentColor" />
                            </Link>
                          </div>
                          <span>Food - Ready To Cook</span>
                        </div>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-6">
                        <div className="img-overlay">
                          <div className="img-color">
                            <Link href="/startup/codelude">
                              <Image
                                src={"/images/startup/codelude/1.jpg"}
                                alt="portfolio"
                                className="img-fluid w-100"
                              />
                            </Link>
                            <div className="caption">
                              <Link
                                href="/startup/codelude"
                                className="btn btn-white">
                                View Details
                              </Link>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="d-flex justify-content-between align-items-center">
                            <h3 className="fw-semibold mb-1">
                              <Link
                                href="/startup/codelude"
                                className="text-inherit">
                                Codelude
                              </Link>
                            </h3>
                            <Link href="/startup/codelude">
                              <ArrowUpRight size={14} fill="currentColor" />
                            </Link>
                          </div>
                          <span>Service - Software </span>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Row>
          </Container>
        </section>
      </main>
    );
}

export default PortfolioSingle