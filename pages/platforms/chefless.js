// import node module libraries
import { useEffect } from 'react'
import { Col, Row, Container, Image } from 'react-bootstrap';
import Link from 'next/link';

// import bootstrap icons
import { ArrowUpRight, ChatQuoteFill } from 'react-bootstrap-icons';
import { CTAButton } from 'widgets';

const PortfolioSingle = () => {
   const title = "Join the Sharif Team & Shape the Future";
   const description = `If you’re passionate about innovation and ready to make an impact, we’d love to meet you. We’re dedicated to supporting your professional development and overall well-being.`;
   const btntext = "Make Investment";
   const btnlink = "#";
    useEffect(() => {
        document.body.className = 'bg-white';
    });

    return (
      <main>
        <section className="py-lg-12 py-7 bg-white">
          <Container>
            <Row>
              <Col md={{ offset: 1, span: 10 }} xs={12}>
                <div className="mb-10">
                  <Row>
                    <Col md={10} xs={12}>
                      <div className="mb-8">
                        <h1 className="display-3 fw-bold mb-4">Chefless</h1>
                        <p className="lead mb-0 pe-lg-8">
                          <PERSON><PERSON> delivers ready-to-cook vegetarian meals and
                          fresh non-veg options, bringing authentic Indian
                          flavors to your kitchen effortlessly.
                        </p>
                      </div>
                    </Col>
                    <Col xs={12}>
                      <Image
                        src={"/images/startup/chefless/1.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={8} xs={12}>
                      <div className="mb-6 mb-lg-0">
                        <h2 className="mb-4">Project Summary</h2>
                        <p className="fs-4 mb-4">
                          Chefless is a startup dedicated to making home-cooked
                          Indian meals more accessible and convenient with
                          ready-to-cook vegetarian and fresh non-veg options. We
                          offer a range of instant gravy mixes, flatbreads, and
                          proteins, allowing customers to enjoy authentic
                          flavors without the hassle of preparation. Our mission
                          is to simplify cooking while delivering delicious,
                          restaurant-quality meals right to your doorstep.
                        </p>
                        <p className="fs-4 mb-0">
                          <h4>Current Focus:</h4>
                          <li>
                            Expanding our ready-to-cook meal subscription
                            service for busy families and individuals.
                          </li>
                          <li>
                            Partnering with local suppliers to ensure the
                            freshest ingredients in all our offerings.
                          </li>
                          <li>
                            Growing our delivery network within Bangalore to
                            reach more customers quickly.
                          </li>
                        </p>
                      </div>
                    </Col>
                    <Col md={{ offset: 1, span: 3 }} xs={12}>
                      <div className="mb-6">
                        <h3 className="mb-4">Services</h3>
                        <ul className="list-unstyled fs-4">
                          <li className="mb-2">Subscription</li>
                          <li className="mb-2">Catering</li>
                          <li className="mb-2">Franchise</li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="mb-3">Website</h3>
                        <Link
                          href="https://chefless.co/"
                          className="fs-4 text-inherit">
                          https://chefless.co/
                        </Link>
                      </div>
                    </Col>
                  </Row>
                </div>
                <hr className="mb-12" />
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">The challenge</h2>
                      </div>
                    </Col>
                    <Col md={9} xs={12}>
                      <p className="fs-4 mb-0">
                        Chefless faced the challenge of providing busy families
                        and working professionals with the convenience of
                        homemade Indian meals without compromising on taste and
                        quality. Ensuring the freshness of ingredients and
                        maintaining consistent quality across all products were
                        additional hurdles that needed to be addressed.
                      </p>
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/chefless/2.png"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/chefless/3.png"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">Solution</h2>
                      </div>
                    </Col>
                    <Col md={9} xs={12}>
                      <p className="fs-4 mb-0">
                        Chefless introduced a comprehensive solution with
                        ready-to-cook meal kits, featuring a variety of instant
                        gravy mixes, flatbreads, and proteins like chicken, egg,
                        and paneer. This approach allowed customers to enjoy
                        quick, authentic Indian meals at home with minimal
                        effort. By leveraging a robust supply chain for fresh
                        ingredients and employing user-friendly ordering through
                        our website and app, Chefless made home cooking both
                        convenient and delightful, catering to the fast-paced
                        lifestyles of modern consumers.
                      </p>
                    </Col>
                    <Col md={12} className="mt-6">
                      <Image
                        src={"/images/startup/chefless/4.png"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={7} xs={12}>
                      <div className="mb-6 mb-md-0">
                        <h2 className="mb-4">Results</h2>
                        <p className="fs-4 mb-0">
                          Chefless has successfully launched its ready-to-cook
                          meal service, delivering convenience and authentic
                          flavors to customers across Bangalore. Our growing
                          customer base and positive feedback highlight the
                          demand for high-quality, easy-to-prepare Indian meals,
                          positioning us for further expansion and innovation in
                          the food delivery market.
                        </p>
                      </div>
                    </Col>
                    <Col md={5} xs={12}>
                      <Row>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">5.4M</h2>
                            <span className="fs-4">Valuation</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">1.8M</h2>
                            <span className="fs-4">Annual Sales</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">12K</h2>
                            <span className="fs-4">Customers</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">45%</h2>
                            <span className="fs-4">Annual Growth</span>
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                  <hr className="mb-8" />
                  <Row className="">
                    <Col xs={12}>
                      <h2 className="h1 mb-8">Related Portfolio</h2>
                    </Col>
                    <Col md={6}>
                      <div className="mb-6">
                        <div className="img-overlay">
                          <div className="img-color">
                            <Link href="/startup/codelude">
                              <Image
                                src={"/images/startup/codelude/1.jpg"}
                                alt="portfolio"
                                className="img-fluid w-100"
                              />
                            </Link>
                            <div className="caption">
                              <Link
                                href="/startup/codelude"
                                className="btn btn-white">
                                View Details
                              </Link>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="d-flex justify-content-between align-items-center">
                            <h3 className="fw-semibold mb-1">
                              <Link
                                href="/startup/codelude"
                                className="text-inherit">
                                Codelude
                              </Link>
                            </h3>
                            <Link href="/startup/franchiseen">
                              <ArrowUpRight size={14} fill="currentColor" />
                            </Link>
                          </div>
                          <span>Service - Software</span>
                        </div>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-6">
                        <div className="img-overlay">
                          <div className="img-color">
                            <Link href="/startup/cuestay">
                              <Image
                                src={"/images/startup/cuestay/1.png"}
                                alt="portfolio"
                                className="img-fluid w-100"
                              />
                            </Link>
                            <div className="caption">
                              <Link
                                href="/startup/cuestay"
                                className="btn btn-white">
                                View Details
                              </Link>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="d-flex justify-content-between align-items-center">
                            <h3 className="fw-semibold mb-1">
                              <Link
                                href="/startup/cuestay"
                                className="text-inherit">
                                Cuestay
                              </Link>
                            </h3>
                            <Link href="/startup/cuestay">
                              <ArrowUpRight size={14} fill="currentColor" />
                            </Link>
                          </div>
                          <span>Retail - Home Accesories</span>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Row>
          </Container>
        </section>
        <CTAButton
          title={title}
          description={description}
          btntext={btntext}
          btnlink={btnlink}
        />
      </main>
    );
}

export default PortfolioSingle