// import node module libraries
import { useEffect } from 'react'
import { Col, Row, Container, Image } from 'react-bootstrap';
import Link from 'next/link';

// import bootstrap icons
import { ArrowUpRight, ChatQuoteFill } from 'react-bootstrap-icons';
import { CTAButton } from 'widgets';

const PortfolioSingle = () => {

  const title = "Join the Investment Revolution";
  const description = `If you’re passionate about innovation and ready to make an impact, we’d love to meet you. We’re dedicated to supporting your professional development and overall well-being.`;
  const btntext = "Make Investment";
  const btnlink = "#";
    useEffect(() => {
        document.body.className = 'bg-white';
    });

    return (
      <main>
        <section className="py-lg-12 py-7 bg-white">
          <Container>
            <Row>
              <Col md={{ offset: 1, span: 10 }} xs={12}>
                <div className="mb-10">
                  <Row>
                    <Col md={10} xs={12}>
                      <div className="mb-8">
                        <h1 className="display-3 fw-bold mb-4">Dietized</h1>
                        <p className="lead mb-0 pe-lg-8">
                          Dietized offers healthy, no-sugar, low-fat meals
                          through subscriptions, a restaurant, and retail
                          products.
                        </p>
                      </div>
                    </Col>
                    <Col xs={12}>
                      <Image
                        src={"/images/startup/dietized/1.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={{ span: 3 }} xs={12}>
                      <div className="mb-6">
                        <h3 className="mb-4">Services</h3>
                        <ul className="list-unstyled fs-4">
                          <li className="mb-2">Meal Subscription</li>
                          <li className="mb-2">Health-Focused Restaurant</li>
                          <li className="mb-2">Retail Product Line</li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="mb-3">Website</h3>
                        <Link
                          href="https://franchiseen.com/"
                          className="fs-4 text-inherit">
                          https://dietized.com/
                        </Link>
                      </div>
                    </Col>
                    <Col md={8} xs={12}>
                      <div className="mb-6 mb-lg-0">
                        <h2 className="mb-4">Project Summary</h2>
                        <p className="fs-4 mb-4">
                          Dietized is committed to transforming healthy eating
                          with flavorful, no-sugar, low-fat meals available via
                          meal subscriptions, a dedicated health-focused
                          restaurant, and a line of retail products.
                        </p>
                        <p className="fs-4 mb-0">
                          <h4>Current Focus:</h4>
                          <li>
                            Scaling the meal subscription service to reach more
                            health-conscious customers.
                          </li>
                          <li>
                            Establishing the restaurant as a top choice for
                            nutritious and delicious dining.
                          </li>
                          <li>
                            Expanding the retail product line to make healthy
                            eating accessible in stores.
                          </li>
                        </p>
                      </div>
                    </Col>
                  </Row>
                </div>
                <hr className="mb-12" />
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">The challenge</h2>
                      </div>
                    </Col>
                    <Col md={{ span: 8 }} xs={12}>
                      <p className="fs-4 mb-0">
                        Dietized needed to balance taste and health in their
                        offerings while scaling operations to meet growing
                        demand and diversify their market reach.
                      </p>
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/dietized/2.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/dietized/4.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">Solution</h2>
                      </div>
                    </Col>
                    <Col md={{ span: 8 }} xs={12}>
                      <p className="fs-4 mb-0">
                        Dietized developed a versatile menu featuring no-sugar,
                        low-fat options that cater to various dietary needs,
                        coupled with a strong marketing strategy and streamlined
                        supply chain management to support growth.
                      </p>
                    </Col>
                    <Col md={12} className="mt-6">
                      <Image
                        src={"/images/startup/dietized/3.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={7} xs={12}>
                      <div className="mb-6 mb-md-0">
                        <h2 className="mb-4">Results</h2>
                        <p className="fs-4 mb-0">
                          Dietized has successfully attracted a loyal customer
                          base, expanded its market presence, and established a
                          strong reputation for providing delicious and healthy
                          meal options.
                        </p>
                      </div>
                    </Col>
                    <Col md={5} xs={12}>
                      <Row>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Valuation</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Annual Sales</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Customers</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">0%</h2>
                            <span className="fs-4">Annual Growth</span>
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </div>
                <hr className="mb-8" />
                <Row className="">
                  <Col xs={12}>
                    <h2 className="h1 mb-8">Related Portfolio</h2>
                  </Col>
                  <Col md={4}>
                    <div className="mb-6">
                      <div className="img-overlay">
                        <div className="img-color">
                          <Link href="/startup/chefless">
                            <Image
                              src={"/images/startup/chefless/1.jpg"}
                              alt="portfolio"
                              className="img-fluid w-100"
                            />
                          </Link>
                          <div className="caption">
                            <Link
                              href="/startup/chefless"
                              className="btn btn-white">
                              View Details
                            </Link>
                          </div>
                        </div>
                      </div>
                      <div className="mt-4">
                        <div className="d-flex justify-content-between align-items-center">
                          <h3 className="fw-semibold mb-1">
                            <Link
                              href="/startup/chefless"
                              className="text-inherit">
                              Chefless
                            </Link>
                          </h3>
                          <Link href="/startup/chefless">
                            <ArrowUpRight size={14} fill="currentColor" />
                          </Link>
                        </div>
                        <span>Food - Ready To Cook</span>
                      </div>
                    </div>
                  </Col>
                  <Col md={4}>
                    <div className="mb-6">
                      <div className="img-overlay">
                        <div className="img-color">
                          <Link href="/startup/codelude">
                            <Image
                              src={"/images/startup/codelude/1.jpg"}
                              alt="portfolio"
                              className="img-fluid w-100"
                            />
                          </Link>
                          <div className="caption">
                            <Link
                              href="/startup/codelude"
                              className="btn btn-white">
                              View Details
                            </Link>
                          </div>
                        </div>
                      </div>
                      <div className="mt-4">
                        <div className="d-flex justify-content-between align-items-center">
                          <h3 className="fw-semibold mb-1">
                            <Link
                              href="/startup/codelude"
                              className="text-inherit">
                              Codelude
                            </Link>
                          </h3>
                          <Link href="/startup/codelude">
                            <ArrowUpRight size={14} fill="currentColor" />
                          </Link>
                        </div>
                        <span>Service - Software</span>
                      </div>
                    </div>
                  </Col>
                  <Col md={4}>
                    <div className="mb-6">
                      <div className="img-overlay">
                        <div className="img-color">
                          <Link href="/startup/hubcv">
                            <Image
                              src={"/images/startup/hubcv/1.jpg"}
                              alt="portfolio"
                              className="img-fluid w-100"
                            />
                          </Link>
                          <div className="caption">
                            <Link
                              href="/startup/hubcv"
                              className="btn btn-white">
                              View Details
                            </Link>
                          </div>
                        </div>
                      </div>
                      <div className="mt-4">
                        <div className="d-flex justify-content-between align-items-center">
                          <h3 className="fw-semibold mb-1">
                            <Link
                              href="/startup/hubcv"
                              className="text-inherit">
                              Hubcv
                            </Link>
                          </h3>
                          <Link href="/startup/hubcv">
                            <ArrowUpRight size={14} fill="currentColor" />
                          </Link>
                        </div>
                        <span>Service - Human Resource</span>
                      </div>
                    </div>
                  </Col>
                </Row>
              </Col>
            </Row>
          </Container>
        </section>
        {/* hero call to action */}
        <CTAButton
          title={title}
          description={description}
          btntext={btntext}
          btnlink={btnlink}
        />
      </main>
    );
}

export default PortfolioSingle