// import node module libraries
import { useEffect } from 'react'
import { Col, Row, Container, Image } from 'react-bootstrap';
import Link from 'next/link';

// import bootstrap icons
import { ArrowUpRight, ChatQuoteFill } from 'react-bootstrap-icons';
import { CTAButton } from 'widgets';

const PortfolioSingle = () => {
   const title = "Join the Sharif Team & Shape the Future";
   const description = `If you’re passionate about innovation and ready to make an impact, we’d love to meet you. We’re dedicated to supporting your professional development and overall well-being.`;
   const btntext = "Make Investment";
   const btnlink = "#";
    useEffect(() => {
        document.body.className = 'bg-white';
    });

    return (
      <main>
        <section className="py-lg-12 py-7 bg-white">
          <Container>
            <Row>
              <Col md={{ offset: 1, span: 10 }} xs={12}>
                <div className="mb-10">
                  <Row>
                    <Col md={10} xs={12}>
                      <div className="mb-8">
                        <h1 className="display-3 fw-bold mb-4">Hubcv</h1>
                        <p className="lead mb-0 pe-lg-8">
                          HubCV is a dynamic platform for building skill-based
                          networks, offering static and dynamic CV creation
                          through comprehensive integration with career
                          management tools.
                        </p>
                      </div>
                    </Col>
                    <Col xs={12}>
                      <Image
                        src={"/images/startup/hubcv/1.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={{ span: 3 }} xs={12}>
                      <div className="mb-6">
                        <h2 className="mb-4">Services</h2>
                        <ul className="list-unstyled fs-4">
                          <li className="mb-2">Dynamic CV Creation</li>
                          <li className="mb-2">Static CV Builder</li>
                          <li className="mb-2">Skill-Based Networking</li>
                        </ul>
                      </div>
                      <div>
                        <h2 className="mb-3">Website</h2>
                        <Link
                          href="https://franchiseen.com/"
                          className="fs-4 text-inherit">
                          https://hubcv.com/
                        </Link>
                      </div>
                    </Col>
                    <Col md={8} xs={12}>
                      <div className="mb-6 mb-lg-0">
                        <h2 className="mb-4">Project Summary</h2>
                        <p className="fs-4 mb-4">
                          HubCV aims to revolutionize professional networking by
                          providing a platform where users can dynamically
                          showcase their skills, achievements, and career
                          progress. By combining traditional CV-building with
                          dynamic, data-driven updates from integrated
                          platforms, HubCV offers a modern solution for personal
                          branding and career development.
                        </p>
                        <p className="fs-4 mb-0">
                          <h4>Current Focus:</h4>
                          <li>
                            Career management
                            tools for comprehensive, real-time professional
                            profiles.
                          </li>
                          <li>
                            Expanding the platforms user base among
                            professionals and students
                          </li>
                          <li>
                            Building a community based on verified skills and career interests.
                          </li>
                        </p>
                      </div>
                    </Col>
                  </Row>
                </div>
                <hr className="mb-12" />
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">The challenge</h2>
                      </div>
                    </Col>
                    <Col md={{ span: 8 }} xs={12}>
                      <p className="fs-4 mb-0">
                        HubCV needed to create a platform that balances user
                        control over their professional narrative (static CV)
                        with the automatic, data-driven presentation of
                        achievements (dynamic CV). Additionally, it required
                        building a robust network where users can connect based
                        on verified skills and experiences.
                      </p>
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/hubcv/2.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/hubcv/3.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">Solution</h2>
                      </div>
                    </Col>
                    <Col md={{ span: 8 }} xs={12}>
                      <p className="fs-4 mb-0">
                        HubCV developed a unique hybrid CV platform that merges
                        static user inputs with dynamic, automated updates from
                        integrated career tools. The platform also features a
                        networking component that allows users to connect based
                        on skills, experiences, and mutual interests.
                      </p>
                    </Col>
                    <Col md={12} className="mt-6">
                      <Image
                        src={"/images/startup/hubcv/4.jpg"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={7} xs={12}>
                      <div className="mb-6 mb-md-0">
                        <h2 className="mb-4">Results</h2>
                        <p className="fs-4 mb-0">
                          HubCV has successfully attracted a diverse user base,
                          providing a modern, dynamic approach to CV building
                          and networking. It has become a preferred platform for
                          professionals looking to showcase their skills
                          dynamically and connect with like-minded individuals.
                        </p>
                      </div>
                    </Col>
                    <Col md={5} xs={12}>
                      <Row>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Valuation</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Annual Sales</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Customers</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">0%</h2>
                            <span className="fs-4">Annual Growth</span>
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </div>
                <hr className="mb-8" />
                <Row className="">
                  <Col xs={12}>
                    <h2 className="h1 mb-8">Related Portfolio</h2>
                  </Col>
                  <Col md={4}>
                    <div className="mb-6">
                      <div className="img-overlay">
                        <div className="img-color">
                          <Link href="/startup/codelude">
                            <Image
                              src={"/images/startup/codelude/1.jpg"}
                              alt="portfolio"
                              className="img-fluid w-100"
                            />
                          </Link>
                          <div className="caption">
                            <Link
                              href="/startup/codelude"
                              className="btn btn-white">
                              View Details
                            </Link>
                          </div>
                        </div>
                      </div>
                      <div className="mt-4">
                        <div className="d-flex justify-content-between align-items-center">
                          <h3 className="fw-semibold mb-1">
                            <Link
                              href="/startup/codelude"
                              className="text-inherit">
                              Codelude
                            </Link>
                          </h3>
                          <Link href="/startup/codelude">
                            <ArrowUpRight size={14} fill="currentColor" />
                          </Link>
                        </div>
                        <span>Service - Software</span>
                      </div>
                    </div>
                  </Col>
                  <Col md={4}>
                    <div className="mb-6">
                      <div className="img-overlay">
                        <div className="img-color">
                          <Link href="/startup/chefless">
                            <Image
                              src={"/images/startup/chefless/1.jpg"}
                              alt="portfolio"
                              className="img-fluid w-100"
                            />
                          </Link>
                          <div className="caption">
                            <Link
                              href="/startup/chefless"
                              className="btn btn-white">
                              View Details
                            </Link>
                          </div>
                        </div>
                      </div>
                      <div className="mt-4">
                        <div className="d-flex justify-content-between align-items-center">
                          <h3 className="fw-semibold mb-1">
                            <Link
                              href="/startup/chefless"
                              className="text-inherit">
                              Chefless
                            </Link>
                          </h3>
                          <Link href="/startup/chefless">
                            <ArrowUpRight size={14} fill="currentColor" />
                          </Link>
                        </div>
                        <span>Food - Ready To Cook</span>
                      </div>
                    </div>
                  </Col>
                  <Col md={4}>
                    <div className="mb-6">
                      <div className="img-overlay">
                        <div className="img-color">
                          <Link href="/startup/dietized">
                            <Image
                              src={"/images/startup/dietized/1.jpg"}
                              alt="portfolio"
                              className="img-fluid w-100"
                            />
                          </Link>
                          <div className="caption">
                            <Link
                              href="/startup/dietized"
                              className="btn btn-white">
                              View Details
                            </Link>
                          </div>
                        </div>
                      </div>
                      <div className="mt-4">
                        <div className="d-flex justify-content-between align-items-center">
                          <h3 className="fw-semibold mb-1">
                            <Link
                              href="/startup/dietized"
                              className="text-inherit">
                              Dietized
                            </Link>
                          </h3>
                          <Link href="/startup/dietized">
                            <ArrowUpRight size={14} fill="currentColor" />
                          </Link>
                        </div>
                        <span>Food - Health Kitchen</span>
                      </div>
                    </div>
                  </Col>
                </Row>
              </Col>
            </Row>
          </Container>
        </section>
      </main>
    );
}

export default PortfolioSingle