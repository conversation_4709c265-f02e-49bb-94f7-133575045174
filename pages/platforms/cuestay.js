// import node module libraries
import { useEffect } from 'react'
import { Col, Row, Container, Image } from 'react-bootstrap';
import Link from 'next/link';

// import bootstrap icons
import { ArrowUpRight, ChatQuoteFill } from 'react-bootstrap-icons';
import { CTAButton } from 'widgets';

const PortfolioSingle = () => {
   const title = "Join the Sharif Team & Shape the Future";
   const description = `If you’re passionate about innovation and ready to make an impact, we’d love to meet you. We’re dedicated to supporting your professional development and overall well-being.`;
   const btntext = "Make Investment";
   const btnlink = "#";
    useEffect(() => {
        document.body.className = 'bg-white';
    });

    return (
      <main>
        <section className="py-lg-12 py-7 bg-white">
          <Container>
            <Row>
              <Col md={{ offset: 1, span: 10 }} xs={12}>
                <div className="mb-10">
                  <Row>
                    <Col md={10} xs={12}>
                      <div className="mb-8">
                        <h1 className="display-3 fw-bold mb-4">Cuestay</h1>
                        <p className="lead mb-0 pe-lg-8">
                          Cuestay is a company that specializes in procuring and
                          managing franchises for creative home items and
                          accessories. Through our innovative e-commerce app,
                          customers can explore unique products and choose
                          between home delivery or in-store shopping. With a
                          focus on quality and creativity, Cuestay aims to make
                          stylish home décor accessible while expanding its
                          franchise network and building a strong brand presence
                          in the home accessories market.
                        </p>
                      </div>
                    </Col>
                    <Col xs={12}>
                      <Image
                        src={"/images/startup/cuestay/1.png"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={8} xs={12}>
                      <div className="mb-6 mb-lg-0">
                        <h2 className="mb-4">Project Summary</h2>
                        <p className="fs-4 mb-4">
                          Cuestay is a dynamic company that specializes in
                          procuring and managing franchises for creative home
                          items and accessories. Our focus is on bringing
                          unique, stylish, and functional products to customers
                          through an extensive network of franchises. With our
                          user-friendly e-commerce app, customers can explore a
                          wide range of home décor and accessories, opt for home
                          delivery, or visit our physical stores for an
                          in-person experience.
                        </p>
                        {/* <p className="fs-4 mb-0">
                          The implementation of this solution led to: Efficient
                          management of franchise operations. Enhanced
                          communication and transparency with shareholders.
                          Successful fundraising efforts to support project
                          development and expansion.
                        </p> */}
                        <p className="fs-4 mb-0">
                          <h4>Current Focus:</h4>
                          <li>
                            Expanding our franchise network for home accessories
                            and creative products.
                          </li>
                          <li>
                            Enhancing our e-commerce platform for seamless
                            shopping experiences.
                          </li>
                          <li>
                            Offering both home delivery and in-store shopping to
                            cater to diverse customer preferences.
                          </li>
                        </p>
                      </div>
                    </Col>
                    <Col md={{ offset: 1, span: 3 }} xs={12}>
                      <div className="mb-6">
                        <h2 className="mb-4">Products</h2>
                        <ul className="list-unstyled fs-4">
                          <li className="mb-2">Procurement & Management</li>
                          <li className="mb-2">E-commerce Platform</li>
                          <li className="mb-2">
                            Home Delivery or In-Store Pickup
                          </li>
                        </ul>
                      </div>
                      <div>
                        <h2 className="mb-3">Website</h2>
                        <Link
                          href="https://cuestay.com/"
                          className="fs-4 text-inherit">
                          https://cuestay.com/
                        </Link>
                      </div>
                    </Col>
                  </Row>
                </div>
                <hr className="mb-12" />
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">The challenge</h2>
                      </div>
                    </Col>
                    <Col md={{ offset: 1, span: 8 }} xs={12}>
                      <p className="fs-4 mb-0">
                        Cuestay operates in the highly competitive home décor
                        market, where the challenge lies in standing out by
                        offering creative, high-quality products. Educating
                        customers on the uniqueness of our offerings and
                        building brand recognition in the market are crucial for
                        sustained growth.
                      </p>
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/cuestay/2.png"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                    <Col md={6} xs={12} className="mt-6">
                      <Image
                        src={"/images/startup/cuestay/3.png"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={3} xs={12}>
                      <div>
                        <h2 className="mb-4">Solution</h2>
                      </div>
                    </Col>
                    <Col md={{ offset: 1, span: 8 }} xs={12}>
                      <p className="fs-4 mb-0">
                        Cuestay bridges the gap between creative home
                        accessories and easy accessibility. Our franchises offer
                        curated products that combine innovation with style,
                        while our app ensures customers can effortlessly browse,
                        order, and receive their items at home or in-store. We
                        are seeking investments to expand our franchise presence
                        and grow our customer base.
                      </p>
                    </Col>
                    <Col md={12} className="mt-6">
                      <Image
                        src={"/images/startup/cuestay/4.png"}
                        alt="portfolio"
                        className="img-fluid w-100 rounded-3"
                      />
                    </Col>
                  </Row>
                </div>
                <div className="mb-10">
                  <Row>
                    <Col md={7} xs={12}>
                      <div className="mb-6 mb-md-0">
                        <h2 className="mb-4">Results</h2>
                        <p className="fs-4 mb-0">
                          Cuestay is in the early stages of building a strong
                          franchise network and establishing its presence in the
                          home décor industry. We are focused on delivering
                          quality products, improving customer experience, and
                          driving brand awareness. Our commitment to creativity
                          and functionality is positioning us for long-term
                          success as we work toward becoming a leading name in
                          the home accessories market.
                        </p>
                      </div>
                    </Col>
                    <Col md={5} xs={12}>
                      <Row>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Valuation</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div className="mb-6">
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Annual Sales</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">0</h2>
                            <span className="fs-4">Customers</span>
                          </div>
                        </Col>
                        <Col xs={6} className="ps-lg-6">
                          <div>
                            <h2 className="fw-bold">0%</h2>
                            <span className="fs-4">Annual Growth</span>
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                  <hr className="my-8" />
                  <Row className="">
                    <Col xs={12}>
                      <h2 className="h1 mb-8">Related Portfolio</h2>
                    </Col>

                    <Col md={6}>
                      <div className="mb-6">
                        <div className="img-overlay">
                          <div className="img-color">
                            <Link href="/startup/chefless">
                              <Image
                                src={"/images/startup/chefless/1.jpg"}
                                alt="portfolio"
                                className="img-fluid w-100"
                              />
                            </Link>
                            <div className="caption">
                              <Link
                                href="/startup/chefless"
                                className="btn btn-white">
                                View Details
                              </Link>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="d-flex justify-content-between align-items-center">
                            <h3 className="fw-semibold mb-1">
                              <Link
                                href="/startup/chefless"
                                className="text-inherit">
                                Chefless
                              </Link>
                            </h3>
                            <Link href="/startup/chefless">
                              <ArrowUpRight size={14} fill="currentColor" />
                            </Link>
                          </div>
                          <span>Food - Ready To Cook</span>
                        </div>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-6">
                        <div className="img-overlay">
                          <div className="img-color">
                            <Link href="/startup/codelude">
                              <Image
                                src={"/images/startup/codelude/1.jpg"}
                                alt="portfolio"
                                className="img-fluid w-100"
                              />
                            </Link>
                            <div className="caption">
                              <Link
                                href="/startup/codelude"
                                className="btn btn-white">
                                View Details
                              </Link>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="d-flex justify-content-between align-items-center">
                            <h3 className="fw-semibold mb-1">
                              <Link
                                href="/startup/codelude"
                                className="text-inherit">
                                Codelude
                              </Link>
                            </h3>
                            <Link href="/startup/codelude">
                              <ArrowUpRight size={14} fill="currentColor" />
                            </Link>
                          </div>
                          <span>Service - Software </span>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Row>
          </Container>
        </section>
        <CTAButton
          title={title}
          description={description}
          btntext={btntext}
          btnlink={btnlink}
        />
      </main>
    );
}

export default PortfolioSingle