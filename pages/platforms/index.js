// import node module libraries
import { Col, Row, Container, Form, Button } from 'react-bootstrap';

// import custom components
import { PortfolioItem } from 'sub-components';

// import data files
import PortfolioData from 'data/marketing/portfolio/PortfolioData'
import Link from 'next/link';

const PortfolioList = () => {
    return (
      <main>
        <section className="py-4 bg-white">
          <Container>
            {/* Page header */}
            <section className="pt-9 pb-9 bg-white ">
              <Container>
                <Row>
                  <Col
                    lg={{ span: 10, offset: 1 }}
                    xl={{ span: 8, offset: 2 }}
                    md={12}
                    sm={12}>
                    <div className="text-center mb-5">
                      <h1 className=" display-2 fw-bold">Platforms</h1>
                      <p className="lead">Our portfolio companies driving innovation and growth</p>
                      {/* Form */}
                      {/* <Link href="/invest">
                        <Button variant="primary" type="submit">
                          Buy Portfolio Shares
                        </Button>
                      </Link> */}
                    </div>
                  </Col>
                </Row>
              </Container>
            </section>
            <Row className=" g-6">
              {PortfolioData.map((item, index) => {
                return (
                  <Col md={4} key={index}>
                    <PortfolioItem item={item} key={index} />;
                  </Col>
                );
              })}
            </Row>
          </Container>
        </section>
      </main>
    );
}

export default PortfolioList