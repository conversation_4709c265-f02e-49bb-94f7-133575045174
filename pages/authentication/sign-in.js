// import node module libraries
import { Fragment, useState } from 'react';
import { Col, Row, Card, Form, Button, Image, Alert } from 'react-bootstrap';
import Link from 'next/link';
import { useRouter } from 'next/router';

// import widget/custom components
import { GeeksSEO } from 'widgets';

// import dashboard layout to override default layout
import DashboardLayout from 'layouts/dashboard/DashboardIndex';

// import appwrite service
import { appwriteService } from 'services/appwrite';

const SignIn = () => {
	const router = useRouter();
	const [formData, setFormData] = useState({
		email: '',
		password: ''
	});
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState('');

	const handleInputChange = (e) => {
		const { name, value } = e.target;
		setFormData(prev => ({
			...prev,
			[name]: value
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setLoading(true);
		setError('');

		try {
			await appwriteService.signIn(formData.email, formData.password);
			// Redirect to dashboard after successful login
			router.push('/dashboard/overview');
		} catch (error) {
			setError(error.message || 'Failed to sign in. Please check your credentials.');
		} finally {
			setLoading(false);
		}
	};

	return (
		<Fragment>
			{/* Geeks SEO settings  */}
			<GeeksSEO title="Sign In | Sharif Ventures" />

			<Row className="align-items-center justify-content-center g-0 min-vh-100">
				<Col lg={5} md={5} className="py-8 py-xl-0">
					<Card>
						<Card.Body className="p-6">
							<div className="mb-4">
								<Link href="/">
									<Image src="/images/brand/logo/logo-icon.svg" className="mb-4" alt="" />
								</Link>
								<h1 className="mb-1 fw-bold">Sign in</h1>
								<span>
									Don’t have an account?{' '}
									<Link href="/authentication/sign-up" className="ms-1">
										Sign up
									</Link>
								</span>
							</div>

							{/* Error Alert */}
							{error && (
								<Alert variant="danger" className="mb-3">
									{error}
								</Alert>
							)}

							{/* Form */}
							<Form onSubmit={handleSubmit}>
								<Row>
									<Col lg={12} md={12} className="mb-3">
										{/* Email */}
										<Form.Label>Email Address</Form.Label>
										<Form.Control
											type="email"
											name="email"
											value={formData.email}
											onChange={handleInputChange}
											placeholder="Enter your email address"
											required
											disabled={loading}
										/>
									</Col>
									<Col lg={12} md={12} className="mb-3">
										{/* Password */}
										<Form.Label>Password</Form.Label>
										<Form.Control
											type="password"
											name="password"
											value={formData.password}
											onChange={handleInputChange}
											placeholder="Enter your password"
											required
											disabled={loading}
										/>
									</Col>
									<Col lg={12} md={12} className="mb-3">
										{/* Checkbox */}
										<div className="d-md-flex justify-content-between align-items-center">
											<Form.Group
												className="mb-3 mb-md-0"
												controlId="formBasicCheckbox"
											>
												<Form.Check type="checkbox" label="Remember me" />
											</Form.Group>
											<Link href="/authentication/forget-password">
												Forgot your password?
											</Link>
										</div>
									</Col>
									<Col lg={12} md={12} className="mb-0 d-grid gap-2">
										{/* Button */}
										<Button
											variant="primary"
											type="submit"
											disabled={loading}
										>
											{loading ? 'Signing in...' : 'Sign in'}
										</Button>
									</Col>
								</Row>
							</Form>
							<hr className="my-4" />
							<div className="mt-4 text-center">
								{/* Facebook */}
								<Link href="#" className="btn-social btn-social-outline btn-facebook">
									<i className="fab fa-facebook"></i>
								</Link>{' '}
								{/* Twitter */}
								<Link href="#" className="btn-social btn-social-outline btn-twitter">
									<i className="fab fa-twitter"></i>
								</Link>{' '}
								{/* LinkedIn */}
								<Link href="#" className="btn-social btn-social-outline btn-linkedin">
									<i className="fab fa-linkedin"></i>
								</Link>{' '}
								{/* GitHub */}
								<Link href="#" className="btn-social btn-social-outline btn-github">
									<i className="fab fa-github"></i>
								</Link>
							</div>
						</Card.Body>
					</Card>
				</Col>
			</Row>
		</Fragment>
	);
};

SignIn.Layout = DashboardLayout;

export default SignIn;
