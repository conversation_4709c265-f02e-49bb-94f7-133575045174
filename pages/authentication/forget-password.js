// import node module libraries
import { Fragment, useState } from 'react';
import { Col, Row, Card, Form, Button, Image, Alert } from 'react-bootstrap';
import Link from 'next/link';

// import widget/custom components
import { GeeksSEO } from 'widgets';

// import dashboard layout to override default layout
import DashboardLayout from 'layouts/dashboard/DashboardIndex';

// import appwrite service
import { appwriteService } from 'services/appwrite';

const ForgetPassword = () => {
	const [formData, setFormData] = useState({
		email: ''
	});
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState('');
	const [success, setSuccess] = useState('');

	const handleInputChange = (e) => {
		const { name, value } = e.target;
		setFormData(prev => ({
			...prev,
			[name]: value
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setLoading(true);
		setError('');
		setSuccess('');

		try {
			await appwriteService.sendPasswordRecovery(formData.email);
			setSuccess('Password reset link has been sent to your email address.');
		} catch (error) {
			setError(error.message || 'Failed to send reset link. Please try again.');
		} finally {
			setLoading(false);
		}
	};

	return (
        <Fragment>
			{/* Geeks SEO settings  */}
			<GeeksSEO title="Forgot Password | Sharif Ventures" />

			<Row className="align-items-center justify-content-center g-0 min-vh-100">
				<Col lg={5} md={5} className="py-8 py-xl-0">
					<Card>
						<Card.Body className="p-6">
							<div className="mb-4">
								<Link href="/">
									<Image src="/images/brand/logo/logo-icon.svg" className="mb-4" alt="" />
								</Link>
								<h1 className="mb-1 fw-bold">Forgot Password</h1>
								<span>Fill the form to reset your password.</span>
							</div>

							{/* Error Alert */}
							{error && (
								<Alert variant="danger" className="mb-3">
									{error}
								</Alert>
							)}

							{/* Success Alert */}
							{success && (
								<Alert variant="success" className="mb-3">
									{success}
								</Alert>
							)}

							{/* Form */}
							<Form onSubmit={handleSubmit}>
								<Row>
									<Col lg={12} md={12} className="mb-3">
										{/* Email */}
										<Form.Label>Email Address</Form.Label>
										<Form.Control
											type="email"
											name="email"
											value={formData.email}
											onChange={handleInputChange}
											placeholder="Enter your email address"
											required
											disabled={loading}
										/>
									</Col>
									<Col lg={12} md={12} className="mb-3 d-grid gap-2">
										{/* Button */}
										<Button
											variant="primary"
											type="submit"
											disabled={loading || success}
										>
											{loading ? 'Sending...' : 'Send Reset Link'}
										</Button>
									</Col>
								</Row>
								<span>
									Return to <Link href="/authentication/sign-in">Sign in</Link>
								</span>
							</Form>
						</Card.Body>
					</Card>
				</Col>
			</Row>
		</Fragment>
    );
};

ForgetPassword.Layout = DashboardLayout;

export default ForgetPassword;
