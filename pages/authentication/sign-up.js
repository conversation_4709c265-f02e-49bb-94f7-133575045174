// import node module libraries
import { Fragment, useState } from 'react';
import { Col, Row, Card, Form, Button, Image, Alert } from 'react-bootstrap';
import Link from 'next/link';
import { useRouter } from 'next/router';

// import widget/custom components
import { GeeksSEO } from 'widgets';

// import dashboard layout to override default layout
import DashboardLayout from 'layouts/dashboard/DashboardIndex';

// import appwrite service
import { appwriteService } from 'services/appwrite';

const SignUp = () => {
	const router = useRouter();
	const [formData, setFormData] = useState({
		name: '',
		email: '',
		password: '',
		agreeToTerms: false
	});
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState('');

	const handleInputChange = (e) => {
		const { name, value, type, checked } = e.target;
		setFormData(prev => ({
			...prev,
			[name]: type === 'checkbox' ? checked : value
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setLoading(true);
		setError('');

		if (!formData.agreeToTerms) {
			setError('Please agree to the Terms of Service and Privacy Policy.');
			setLoading(false);
			return;
		}

		try {
			await appwriteService.signUp(formData.email, formData.password, formData.name);
			// Redirect to dashboard after successful registration
			router.push('/dashboard/overview');
		} catch (error) {
			setError(error.message || 'Failed to create account. Please try again.');
		} finally {
			setLoading(false);
		}
	};

	return (
		<Fragment>
			{/* Geeks SEO settings  */}
			<GeeksSEO title="Sign Up | Sharif Ventures" />

			<Row className="align-items-center justify-content-center g-0 min-vh-100">
				<Col lg={5} md={5} className="py-8 py-xl-0">
					<Card>
						<Card.Body className="p-6">
							<div className="mb-4">
								<Link href="/">
									<Image src="/images/brand/logo/logo-icon.svg" className="mb-4" alt="" />
								</Link>
								<h1 className="mb-1 fw-bold">Sign up</h1>
								<span>
									Already have an account?{' '}
									<Link href="/authentication/sign-in" className="ms-1">
										Sign in
									</Link>
								</span>
							</div>

							{/* Error Alert */}
							{error && (
								<Alert variant="danger" className="mb-3">
									{error}
								</Alert>
							)}

							{/* Form */}
							<Form onSubmit={handleSubmit}>
								<Row>
									<Col lg={12} md={12} className="mb-3">
										{/* User Name */}
										<Form.Label>Full Name</Form.Label>
										<Form.Control
											type="text"
											name="name"
											value={formData.name}
											onChange={handleInputChange}
											placeholder="Enter your full name"
											required
											disabled={loading}
										/>
									</Col>
									<Col lg={12} md={12} className="mb-3">
										{/* Email */}
										<Form.Label>Email Address</Form.Label>
										<Form.Control
											type="email"
											name="email"
											value={formData.email}
											onChange={handleInputChange}
											placeholder="Enter your email address"
											required
											disabled={loading}
										/>
									</Col>
									<Col lg={12} md={12} className="mb-3">
										{/* Password */}
										<Form.Label>Password</Form.Label>
										<Form.Control
											type="password"
											name="password"
											value={formData.password}
											onChange={handleInputChange}
											placeholder="Enter your password"
											required
											disabled={loading}
										/>
									</Col>
									<Col lg={12} md={12} className="mb-3">
										{/* Checkbox */}
										<Form.Check type="checkbox" id="check-api-checkbox">
											<Form.Check.Input
												type="checkbox"
												name="agreeToTerms"
												checked={formData.agreeToTerms}
												onChange={handleInputChange}
												disabled={loading}
											/>
											<Form.Check.Label>
												I agree to the{' '}
												<Link href="/marketing/specialty/terms-and-conditions/">
													Terms of Service
												</Link>{' '}
												and{' '}
												<Link href="/marketing/specialty/terms-and-conditions/">
													Privacy Policy.
												</Link>
											</Form.Check.Label>
										</Form.Check>
									</Col>
									<Col lg={12} md={12} className="mb-0 d-grid gap-2">
										{/* Button */}
										<Button
											variant="primary"
											type="submit"
											disabled={loading}
										>
											{loading ? 'Creating Account...' : 'Sign up'}
										</Button>
									</Col>
								</Row>
							</Form>
							<hr className="my-4" />
							<div className="mt-4 text-center">
								{/* Facebook */}
								<Link href="#" className="btn-social btn-social-outline btn-facebook">
									<i className="fab fa-facebook"></i>
								</Link>{' '}
								{/* Twitter */}
								<Link href="#" className="btn-social btn-social-outline btn-twitter">
									<i className="fab fa-twitter"></i>
								</Link>{' '}
								{/* LinkedIn */}
								<Link href="#" className="btn-social btn-social-outline btn-linkedin">
									<i className="fab fa-linkedin"></i>
								</Link>{' '}
								{/* GitHub */}
								<Link href="#" className="btn-social btn-social-outline btn-github">
									<i className="fab fa-github"></i>
								</Link>
							</div>
						</Card.Body>
					</Card>
				</Col>
			</Row>
		</Fragment>
	);
};

SignUp.Layout = DashboardLayout;

export default SignUp;