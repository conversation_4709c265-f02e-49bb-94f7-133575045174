// import node module libraries
import { Fragment } from 'react';

// import sub components
import { CareerAtGeeks, TeamMemberAtGeeks, GeeksCulture } from 'sub-components';

// import widget/custom components
import { CTAButton, GeeksSEO } from 'widgets';

// import your layout to override default layout 
import DefaultLayout from 'layouts/marketing/DefaultLayout';
import { Col, Container, Row } from 'react-bootstrap';

const Career = () => {
	const title = "Join the Investment Revolution";
  const description = `If you’re passionate about innovation and ready to make an impact, we’d love to meet you. We’re dedicated to supporting your professional development and overall well-being.`;
  const btntext = "Make Investment";
	const btnlink = '/invest';

	return (
    <Fragment>
      {/* Geeks SEO settings  */}
      <GeeksSEO title="Career | Sharif" />

      <main>
        {/* collage gallery */}
        {/* <Collage /> */}
        {/* hero gradient */}
        <section className="pt-14 pb-9 bg-white ">
          <Container>
            <Row>
              <Col
                lg={{ span: 10, offset: 1 }}
                xl={{ span: 8, offset: 2 }}
                md={12}
                sm={12}>
                <div className="text-center mb-5">
                  <h1 className=" display-2 fw-bold">About Us</h1>
                  <p className="lead">
                    Empowering Startups, Simplifying Funding
                  </p>
                  {/* Form */}
                  {/* <Link href="/invest">
                        <Button variant="primary" type="submit">
                          Buy Portfolio Shares
                        </Button>
                      </Link> */}
                </div>
              </Col>
            </Row>
          </Container>
        </section>

        {/* geeks culture */}
        <GeeksCulture />

        {/* career at geeks */}
        {/* <CareerAtGeeks /> */}

        {/* team member at geeks */}
        <TeamMemberAtGeeks />

        {/* hero call to action */}
        {/* <CTAButton
          title={title}
          description={description}
          btntext={btntext}
          btnlink={btnlink}
        /> */}
        {/* <CTAButton
					title={title}
					description={description}
					btntext={btntext}
					btnlink={btnlink}
				/> */}
      </main>
    </Fragment>
  );
};

Career.Layout = DefaultLayout;

export default Career;
