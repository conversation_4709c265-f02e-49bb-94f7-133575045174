// import node module libraries
import { Fragment, useContext } from 'react';
import { DragDropContext } from 'react-beautiful-dnd';
import { Col, Row, Form } from 'react-bootstrap';
import { useSelector} from 'react-redux'

// import widget/custom components
import { FormSelect }  from 'widgets';

// import sub components
import { KanbanColumn }  from 'sub-components';

// import hook file
import useTaskKanban from 'hooks/useTaskKanban';

const Kanban = () => {
	
	/* Redux get data */
	
	const taskList = useSelector((state) => state.kanban.taskList)

	const { handleDragEnd } = useTaskKanban();

	const filterOptions = [
		{ value: 'Just my task', label: 'Just my task' },
		{ value: 'Due this week', label: 'Due this week' },
		{ value: 'Due next week', label: 'Due next week' }
	];
	const sortOptions = [
		{ value: 'None', label: 'None' },
		{ value: 'Due Date', label: 'Due Date' },
		{ value: 'Assignee', label: 'Assignee' },
		{ value: 'Likes', label: 'Likes' },
		{ value: 'Alphabetical', label: 'Alphabetical' },
		{ value: 'Priority', label: 'Priority' }
	];

	return (
    <Fragment>
      <Row className="mb-4 align-items-center">
        <Col xxl={10} xl={8} lg={6} md={12} xs={12} className="mb-3 mb-lg-0">
          <h1 className="h2 fw-bold">FUNDRAISING</h1>
        </Col>
        <Col xxl={1} xl={2} lg={3} md={12} className="pe-lg-2">
          <Form.Control
            as={FormSelect}
            placeholder="Sort"
            options={sortOptions}
          />
        </Col>
        <Col xxl={1} xl={2} lg={3} md={12} className="pe-lg-2">
          <Form.Control
            as={FormSelect}
            placeholder="Filter"
            options={filterOptions}
          />
        </Col>
      </Row>

      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="task-kanban-container pb-6">
          {taskList.map((item, index) => {
            return <KanbanColumn columnData={item} key={index} />;
          })}
        </div>
      </DragDropContext>
    </Fragment>
  );
};

export default Kanban;
