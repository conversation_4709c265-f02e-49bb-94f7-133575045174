// import node module libraries
import React, { Fragment, useEffect } from 'react'

// import sub components
import {
    EducationHeroRightImage,
} from 'sub-components';

// import your layout to override default layout 
import DefaultLayout from 'layouts/marketing/DefaultLayout';

const LandingEducation = () => {
    useEffect(() => {
        document.body.className = 'bg-white';
    });

    return (
      <Fragment>
        <main>

          {/*  learn today hero section */}
          <EducationHeroRightImage />
          
        </main>
      </Fragment>
    );
}

LandingEducation.Layout = DefaultLayout;

export default LandingEducation