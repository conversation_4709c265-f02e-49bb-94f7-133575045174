// import node module libraries
import { Fragment } from 'react';
import { Col, Row, Container } from 'react-bootstrap';

// import widget/custom components
import { GeeksSEO } from 'widgets';

// import sub components
import { ApplyForm } from 'sub-components';

// import your layout to override default layout 
import LayoutFooterLinks from 'layouts/marketing/LayoutFooterLinks';

const CareerSingle = () => {
	return (
    <Fragment>
      {/* Geeks SEO settings  */}
      <GeeksSEO title="Career Single | Sharif" />

      <main>
        <section className="pt-14 bg-white">
          <Container>
            <Row>
              <Col
                xl={{ span: 6, offset: 3 }}
                lg={{ span: 8, offset: 2 }}
                xs={12}>
                <div className="mb-5">
                  {/* heading */}
                  <div className="text-center mb-6">
                    <h1 className="display-3 mb-4 fw-bold">
                      Human Resources (HR) Manager
                    </h1>
                    Dubai
                  </div>
                  <h2>Job Summary</h2>
                  <p>
                    The HR Manager is responsible for overseeing all human
                    resource operations and ensuring they align with the
                    business goals. This role involves managing recruitment,
                    employee relations, performance management, training,
                    compliance, and overall HR strategy. The HR Manager will
                    also play a critical role in fostering a positive company
                    culture and enhancing employee engagement.
                  </p>

                  {/* heading */}
                  <div className="mt-8">
                    <h2>Key Responsibilities</h2>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        1. Recruitment & Talent Acquisition:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Develop and implement recruitment strategies to
                          attract top talent for all positions.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Oversee the full recruitment cycle, from job posting
                          and sourcing to interviewing and onboarding.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Collaborate with department heads to understand
                          staffing needs and provide guidance on workforce
                          planning.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Leverage LinkedIn Recruiter and other platforms to
                          source potential candidates and build a talent
                          pipeline.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        2. Employee Relations & Engagement:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Serve as the main point of contact for employee
                          issues, including resolving conflicts, addressing
                          grievances, and providing counseling.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Promote a positive work environment by fostering open
                          communication, recognition programs, and employee
                          engagement initiatives.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Organize team-building activities and events to
                          strengthen company culture and morale.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Utilize LinkedIn Learning to provide employees with
                          access to continuous learning and development
                          opportunities.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        3. Performance Management:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Design and implement performance management processes,
                          including setting goals, conducting performance
                          reviews, and providing ongoing feedback.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Identify employee development needs and create
                          training programs to improve performance and skills.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Work closely with managers to ensure alignment of
                          employee performance with organizational goals.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Encourage the use of LinkedIn Skills assessments to
                          track employee competencies and areas for improvement.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        4. Compliance & Policies:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Ensure compliance with labor laws and regulations,
                          including employee contracts, health and safety
                          standards, and other HR-related legislation.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Develop, update, and enforce company policies,
                          procedures, and employee handbooks.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Handle disciplinary actions and terminations in
                          compliance with company policies and legal
                          requirements.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        5. Compensation & Benefits:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Manage the company’s compensation and benefits
                          programs, ensuring they are competitive and in line
                          with industry standards.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Oversee payroll processing, leave management, and
                          employee benefits administration.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Conduct market research using LinkedIn Salary Insights
                          to recommend salary adjustments and benefit
                          enhancements.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        6. Learning & Development:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Identify training needs across departments and create
                          learning programs to improve employee performance.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Facilitate leadership development and succession
                          planning programs.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Organize workshops, seminars, and professional
                          development opportunities for employees.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Use LinkedIn Learning as a tool for continuous
                          employee development and upskilling.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        7. HR Strategy & Planning:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Develop and implement HR strategies aligned with the
                          companys long-term objectives.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Forecast staffing needs and create plans to meet
                          future workforce requirements.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Provide strategic HR support to senior leadership,
                          advising on organizational structure, change
                          management, and workforce development.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        8. HR Metrics & Reporting:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Maintain accurate employee records and generate HR
                          reports on key metrics such as turnover, retention,
                          and employee satisfaction.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Monitor and analyze HR trends, providing insights to
                          improve HR practices and employee retention.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Conduct exit interviews and provide feedback to
                          leadership to drive continuous improvement.
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div className="mt-8">
                    {/* heading */}
                    <h2>Qualifications:</h2>

                    <div className="mt-4">
                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Bachelor’s degree in Human Resources, Business
                          Administration, or a related field (Master’s degree or
                          HR certification is a plus).
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>
                          5+ years of experience in human resources, with a
                          focus on recruitment, employee relations, and
                          performance management.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Thorough understanding of labor laws, employment
                          standards, and HR best practices.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Proven leadership, organizational, and problem-solving
                          skills.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Strong interpersonal and communication abilities.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Experience with HR software and systems (e.g., HRIS,
                          payroll systems).
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Proficiency in using LinkedIn for talent acquisition,
                          employer branding, and networking.
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div className="mt-8">
                    {/* heading */}
                    <h2>Key Competencies:</h2>

                    <div className="mt-4">
                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Strong decision-making and problem-solving skills.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>
                          Excellent interpersonal and conflict-resolution
                          abilities.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          High level of professionalism, confidentiality, and
                          integrity.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Ability to multitask and manage various HR functions
                          simultaneously.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Strong leadership and team management skills.
                        </li>
                      </ul>
                    </div>
                  </div>

                  {/* form to apply for this job */}
                  <ApplyForm />
                </div>
              </Col>
            </Row>
          </Container>
        </section>
      </main>
    </Fragment>
  );
};

export default CareerSingle;
