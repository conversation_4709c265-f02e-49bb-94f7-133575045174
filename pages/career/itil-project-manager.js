// import node module libraries
import { Fragment } from 'react';
import { Col, Row, Container } from 'react-bootstrap';

// import widget/custom components
import { GeeksSEO } from 'widgets';

// import sub components
import { ApplyForm } from 'sub-components';

// import your layout to override default layout 
import LayoutFooterLinks from 'layouts/marketing/LayoutFooterLinks';

const CareerSingle = () => {
	return (
    <Fragment>
      {/* Geeks SEO settings  */}
      <GeeksSEO title="ITIL Project Manager | Sharif" />

      <main>
        <section className="pt-14 bg-white">
          <Container>
            <Row>
              <Col
                xl={{ span: 6, offset: 3 }}
                lg={{ span: 8, offset: 2 }}
                xs={12}>
                <div className="mb-5">
                  {/* heading */}
                  <div className="text-center mb-6">
                    <h1 className="display-3 mb-4 fw-bold">
                      ITIL Project Manager
                    </h1>
                    Dubai
                  </div>
                  <h2>Job Summary</h2>
                  <p>
                    The ITIL Project Manager is responsible for overseeing IT
                    service management (ITSM) projects, ensuring that IT
                    services are aligned with business objectives and delivered
                    according to ITIL (Information Technology Infrastructure
                    Library) best practices. This role involves managing the
                    full lifecycle of IT projects, including planning,
                    execution, monitoring, and ensuring effective implementation
                    of ITIL processes such as incident, problem, change, and
                    service management.
                  </p>

                  {/* heading */}
                  <div className="mt-8">
                    <h2>Key Responsibilities</h2>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">1. Project Management:</h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Lead and manage IT service management projects,
                          ensuring they are delivered on time, within scope, and
                          within budget.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Develop detailed project plans, timelines, and
                          milestones, and ensure clear communication of project
                          objectives to all stakeholders.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Monitor project progress, identify risks, and take
                          proactive measures to mitigate them.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        2. ITIL Process Implementation:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Implement and manage ITIL processes such as incident
                          management, change management, problem management, and
                          service level management.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Ensure that IT services are aligned with the
                          business’s needs and follow ITIL best practices.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Work with teams to improve ITIL processes, ensuring
                          continuous service improvement.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        3. Service Delivery & Performance Management:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Ensure IT services are delivered according to Service
                          Level Agreements (SLAs), managing performance and
                          quality throughout the service lifecycle.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Collaborate with IT operations and service delivery
                          teams to monitor and improve service performance.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Drive root cause analysis for incidents and problems,
                          ensuring timely resolution and minimizing business
                          impact.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        4. Stakeholder & Client Management:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Serve as the primary point of contact for stakeholders
                          regarding IT service management projects.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Liaise with business units and clients to understand
                          their requirements, providing regular updates on
                          project status and service performance.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Manage stakeholder expectations and ensure high levels
                          of customer satisfaction.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">5. Change Management:</h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Manage the change management process, ensuring changes
                          to IT services are planned, approved, and implemented
                          with minimal disruption.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Coordinate with cross-functional teams to ensure
                          successful change implementation, adhering to ITIL
                          guidelines.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Maintain a change log and ensure compliance with
                          change management policies.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        6. Risk & Issue Management:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Identify, manage, and mitigate risks related to IT
                          service management projects.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Track and resolve project issues, ensuring minimal
                          impact on service delivery and project timelines.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Report on risk management activities and escalate
                          critical issues to senior management.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        7. Continuous Service Improvement (CSI):
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Identify opportunities for process improvements and
                          cost efficiencies within IT service management.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Implement and track improvements through the
                          Continuous Service Improvement (CSI) process.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Collaborate with teams to drive a culture of
                          continuous improvement in IT service delivery.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        8. Budget & Resource Management:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Manage project budgets, tracking expenses, and
                          ensuring projects are delivered within financial
                          constraints.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Allocate resources effectively to meet project
                          requirements and ensure successful delivery.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Work with finance teams to forecast and report on
                          project financials.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        9. Reporting & Documentation:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Develop and maintain detailed project documentation,
                          including project charters, status reports, and
                          post-implementation reviews.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Provide regular project updates to senior management,
                          highlighting achievements, risks, and ongoing
                          activities.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Ensure that all ITIL processes are documented and
                          aligned with organizational standards.
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div className="mt-8">
                    {/* heading */}
                    <h2>Qualifications:</h2>

                    <div className="mt-4">
                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Bachelor’s degree in Information Technology, Computer
                          Science, Business Management, or a related field.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>
                          5+ years of experience in IT project management, with
                          a strong focus on ITIL service management.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          ITIL v3 or ITIL 4 Foundation certification
                          (Intermediate or Expert level preferred).
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Proven experience in delivering IT projects within an
                          ITIL framework.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Strong understanding of IT service management (ITSM)
                          tools (e.g., ServiceNow, BMC Remedy, or equivalent).
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Experience with Agile or Scrum methodologies is a
                          plus.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          PMP or PRINCE2 certification is a plus.
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div className="mt-8">
                    {/* heading */}
                    <h2>Key Competencies:</h2>

                    <div className="mt-4">
                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Strong project management and organizational skills.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>
                          Excellent communication and stakeholder management
                          abilities.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          In-depth understanding of ITIL processes and service
                          delivery.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Ability to manage multiple projects simultaneously and
                          meet deadlines.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Analytical mindset and strong problem-solving
                          abilities.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Strong leadership and team management skills.
                        </li>
                      </ul>
                    </div>
                  </div>

                  {/* form to apply for this job */}
                  <ApplyForm />
                </div>
              </Col>
            </Row>
          </Container>
        </section>
      </main>
    </Fragment>
  );
};

export default CareerSingle;
