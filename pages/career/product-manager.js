// import node module libraries
import { Fragment } from 'react';
import { Col, Row, Container } from 'react-bootstrap';

// import widget/custom components
import { GeeksSEO } from 'widgets';

// import sub components
import { ApplyForm } from 'sub-components';

// import your layout to override default layout 
import LayoutFooterLinks from 'layouts/marketing/LayoutFooterLinks';

const CareerSingle = () => {
	return (
    <Fragment>
      {/* Geeks SEO settings  */}
      <GeeksSEO title="Product Manager | Sharif" />

      <main>
        <section className="pt-14 bg-white">
          <Container>
            <Row>
              <Col
                xl={{ span: 6, offset: 3 }}
                lg={{ span: 8, offset: 2 }}
                xs={12}>
                <div className="mb-5">
                  {/* heading */}
                  <div className="text-center mb-6">
                    <h1 className="display-3 mb-4 fw-bold">Product Manager</h1>
                    Dubai
                  </div>
                  <h2>Job Summary</h2>
                  <p>
                    The Product Manager is responsible for the entire lifecycle
                    of a product, from concept to launch. This role involves
                    defining product vision, gathering and prioritizing product
                    requirements, collaborating with cross-functional teams, and
                    ensuring that the product supports the company’s overall
                    strategy and goals. The Product Manager acts as a bridge
                    between the business, development, marketing, and user teams
                    to deliver high-quality products that meet customer needs
                    and drive business growth.
                  </p>

                  {/* heading */}
                  <div className="mt-8">
                    <h2>Key Responsibilities</h2>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        1. Product Strategy & Roadmap:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Define the product vision, strategy, and roadmap
                          aligned with business goals and market opportunities.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Conduct market research to understand customer needs,
                          market trends, and the competitive landscape.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Prioritize product features and enhancements based on
                          business value, customer impact, and technical
                          feasibility.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        2. Product Development & Lifecycle Management:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Manage the entire product lifecycle from ideation to
                          development, launch, and post-launch evaluation.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Collaborate with engineering, design, and marketing
                          teams to ensure timely and successful delivery of
                          products.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Oversee product releases, ensuring new features are
                          well-tested and meet customer expectations.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        3. Customer & Market Research:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Engage with customers, stakeholders, and sales teams
                          to gather feedback and understand pain points and
                          requirements.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Conduct user testing, surveys, and interviews to
                          validate product concepts and improve the user
                          experience.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Use data analytics to assess product performance,
                          identify trends, and inform product decisions.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        4. Cross-functional Collaboration:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Work closely with engineering and design teams to
                          translate product requirements into detailed
                          specifications and user stories.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Collaborate with marketing and sales teams to develop
                          go-to-market strategies and ensure successful product
                          launches.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Act as a liaison between business and technical teams
                          to ensure product alignment and prioritize efforts.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        5. Feature Definition & Prioritization:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Define and document product features, user stories,
                          and acceptance criteria.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Develop and manage a product backlog, prioritizing
                          features and enhancements based on customer feedback
                          and business needs.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Work with development teams using Agile methodologies
                          to plan sprints and release schedules.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        6. Go-to-Market & Launch Planning:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Develop and execute go-to-market plans for new product
                          launches, working closely with marketing, sales, and
                          customer support teams.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Create product positioning and messaging that
                          highlights the unique value proposition of the
                          product.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Coordinate product training for sales teams and ensure
                          all necessary marketing materials and documentation
                          are ready for launch.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        7. Key Metrics & Reporting:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Define and track key performance indicators (KPIs) to
                          measure product success (e.g., user engagement,
                          adoption, revenue growth).
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Analyze product data and provide insights to drive
                          continuous improvement.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Regularly report on product performance, roadmap
                          progress, and customer feedback to stakeholders and
                          senior management.
                        </li>
                      </ul>
                    </div>

                    <div className="mt-4">
                      <h3 className="d-flex mb-3 ">
                        8. Post-Launch Product Management:
                      </h3>

                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Monitor product performance post-launch, identifying
                          opportunities for improvements or feature
                          enhancements.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Address any issues or challenges that arise after
                          launch, ensuring a positive customer experience.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Continuously gather feedback and refine the product
                          based on customer needs and market dynamics.
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div className="mt-8">
                    {/* heading */}
                    <h2>Qualifications:</h2>

                    <div className="mt-4">
                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Bachelor’s degree in Business, Engineering, Computer
                          Science, or a related field (MBA is a plus).
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>
                          3–5 years of experience in product management or a
                          related role (experience with digital products
                          preferred).
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Proven experience leading cross-functional teams to
                          deliver high-quality products.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Strong understanding of product lifecycle management
                          and Agile development methodologies.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Proficiency in product management tools (e.g., JIRA,
                          Trello, Confluence) and data analytics platforms
                          (e.g., Google Analytics).
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Excellent communication, presentation, and leadership
                          skills.
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div className="mt-8">
                    {/* heading */}
                    <h2>Key Competencies:</h2>

                    <div className="mt-4">
                      {/* list */}
                      <ul className="list-unstyled">
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>
                          Strong problem-solving and analytical skills.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>
                          Ability to balance strategic thinking with attention
                          to detail.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary mt-1"></i>{" "}
                          Customer-focused mindset, with a passion for
                          delivering value.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Excellent organizational and time-management skills,
                          with the ability to manage multiple projects
                          simultaneously.
                        </li>
                        <li className="d-flex mb-4 ">
                          <i className="fe fe-check-circle me-2 text-primary  mt-1"></i>{" "}
                          Ability to thrive in a fast-paced, dynamic
                          environment.
                        </li>
                      </ul>
                    </div>
                  </div>

                  {/* form to apply for this job */}
                  <ApplyForm />
                </div>
              </Col>
            </Row>
          </Container>
        </section>
      </main>
    </Fragment>
  );
};

export default CareerSingle;
