// import node module libraries
import Head from 'next/head';
import { useRouter } from 'next/router'
import { NextSeo } from 'next-seo'

// import provider and store from redux state management.
import { Provider } from 'react-redux'
import { store } from 'store/store'

// import theme style scss file
import 'styles/theme.scss';

// import default layouts
import DefaultMarketingLayout from 'layouts/marketing/DefaultLayout';
import DefaultDashboardLayout from 'layouts/dashboard/DashboardIndex';
import { Fragment } from 'react';

function MyApp({ Component, pageProps }) {
  const router = useRouter();
  const pageURL = process.env.baseURL + router.pathname;
  const title =
    "Sharif Ventures - Empowering Business Growth with Innovative Funding Solutions";
  const description =
    "Discover Sharif, where we revolutionize business funding through transparency, trust, and cutting-edge technology, helping businesses reach their full potential.";
  const keywords =
    "Sharif Ventures, Business funding, Venture capital, Innovative funding solutions, Business growth, Financial opportunities, Business financing, Investment opportunities, Start-up funding, Growth funding";

  // Identify the layout, which will be applied conditionally
  const Layout = Component.Layout || (router.pathname.includes('dashboard') ? (router.pathname.includes('instructor') || router.pathname.includes('student') ? DefaultMarketingLayout : DefaultDashboardLayout) : DefaultMarketingLayout)

  return (
    <Fragment>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="keywords" content={keywords} />
        <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
      </Head>
      <NextSeo
        title={title}
        description={description}
        canonical={pageURL}
        openGraph={{
          url: pageURL,
          title: title,
          description: description,
          site_name: process.env.siteName,
          images: [
            {
              url: '/images/og/seo.jpg',
              width: 1200,
              height: 630,
              alt: 'Sharif SEO Image',
            },
          ],
        }}
      />
      <Provider store={store}>
        <Layout>
          <Component {...pageProps} />
        </Layout>
      </Provider>
    </Fragment>
  )
}

export default MyApp
