// import node module libraries
import { <PERSON>, Container, Tab } from 'react-bootstrap';

// import widget/custom components
import { GridListViewButton, GeeksSEO } from 'widgets';

// import sub components
import StudentsGridCard from './StudentsGridCard';
import StudentsListCard from './StudentsListCard';

// import profile layout wrapper
import ProfileLayout from 'layouts/marketing/instructor/ProfileLayout';

const Students = () => {
	return (
    <>
      {/* Geeks SEO settings  */}
      <GeeksSEO title="Instructor Students | Geeks Nextjs Template" />
      <Container className='mt-12'>
        <Tab.Container defaultActiveKey="grid">
          <Card className="mb-4 pb-1">
            <Card.Header className="border-0 d-flex justify-content-between ">
              <div className="mb-3 mb-lg-0">
                <h3 className="mb-0">Shareholders</h3>
                <p className="mb-0">Our current shareholders invested in our platforms.</p>
              </div>
              {/* <GridListViewButton keyGrid="grid" keyList="list" /> */}
            </Card.Header>
          </Card>
          <Tab.Content>
            <Tab.Pane eventKey="grid" className="pb-4">
              {/* students in grid view */}
              <Card className="bg-transparent shadow-none">
                <Card.Body className="px-0 py-0">
                  <StudentsGridCard />
                </Card.Body>
              </Card>
              {/* end of students in grid view */}
            </Tab.Pane>
            <Tab.Pane eventKey="list" className="pb-4">
              {/* students in list view */}
              <Card className="">
                <Card.Body className="px-0 py-0">
                  <StudentsListCard />
                  {/* end of students in list view */}
                </Card.Body>
              </Card>
            </Tab.Pane>
          </Tab.Content>
        </Tab.Container>
      </Container>
    </>
  );
};

export default Students;
