// import node module libraries
import { Fragment } from 'react';
import Link from 'next/link';
import { Col, Row, Container } from 'react-bootstrap';

// import widget/custom components
import { GeeksSEO } from 'widgets';

// import custom components
import InvestorRegistrationForm from 'components/InvestorRegistrationForm';

// import your layout to override default layout
import DefaultLayout from 'layouts/marketing/DefaultLayout';

const InvestorRegistration = () => {
	return (
    <Fragment>
      {/* Geeks SEO settings  */}
      <GeeksSEO title="Investor Registration | Sharif Ventures" />
      <main>
        <section className="bg-light py-5">
          <Container>
            <Row className="justify-content-center">
              <Col lg={10} xl={8}>
                {/* Header Section */}
                <div className="text-center mb-5">
                  <h1 className="display-5 fw-bold text-dark mb-3">
                    Join Our Investment Community
                  </h1>
                  <p className="lead text-muted mb-4">
                    Thank you for your interest in investing with Sharif Ventures.
                    Complete this registration to explore opportunities in our portfolio of innovative startups.
                  </p>
                  <div className="row justify-content-center">
                    <div className="col-md-8">
                      <p className="text-muted">
                        Our team will review your application and contact you within 2-3 business days
                        to discuss eligibility criteria and schedule a meeting.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Registration Form */}
                <InvestorRegistrationForm />
              </Col>
            </Row>
          </Container>
        </section>
      </main>
    </Fragment>
  );
};

InvestorRegistration.Layout = DefaultLayout;

export default InvestorRegistration;
