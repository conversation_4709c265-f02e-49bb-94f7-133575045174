# Appwrite Database Setup for Investor Registration

This document explains how to set up the Appwrite database for the investor registration form.

## Prerequisites

1. Appwrite Cloud account or self-hosted Appwrite instance
2. Project ID: `669b682f0002f3bf6887` (already configured in the code)

## Database Setup

### 1. Create Database

1. Go to your Appwrite Console
2. Navigate to "Databases" section
3. Click "Create Database"
4. Set Database ID: `669b68a40003b8b5b8b8`
5. Set Database Name: `sharif_ventures`

### 2. Create Collection

1. Inside the database, click "Create Collection"
2. Set Collection ID: `investors`
3. Set Collection Name: `Investor Registrations`

### 3. Configure Collection Attributes

Add the following attributes to the `investors` collection:

#### Personal Information
- `firstName` (String, 100 characters, Required)
- `lastName` (String, 100 characters, Required)
- `email` (Email, 255 characters, Required)
- `phone` (String, 20 characters, Required)
- `dateOfBirth` (DateTime, Required)
- `nationality` (String, 100 characters, Required)

#### Professional Information
- `occupation` (String, 100 characters, Required)
- `company` (String, 200 characters, Required)
- `jobTitle` (String, 150 characters, Required)
- `workExperience` (String, 50 characters, Required)

#### Investment Information
- `investmentExperience` (String, 50 characters, Required)
- `investmentRange` (String, 50 characters, Required)
- `riskTolerance` (String, 50 characters, Required)
- `investmentGoals` (String, 100 characters, Required)
- `preferredSectors` (String, 1000 characters, Required) - JSON array

#### Financial Information
- `annualIncome` (String, 50 characters, Required)
- `netWorth` (String, 50 characters, Required)
- `liquidAssets` (String, 50 characters, Required)

#### Additional Information
- `hearAboutUs` (String, 100 characters, Required)
- `additionalComments` (String, 2000 characters, Optional)
- `agreeToTerms` (Boolean, Required)
- `agreeToMarketing` (Boolean, Required)

#### System Fields
- `createdAt` (DateTime, Required)
- `status` (String, 50 characters, Required, Default: "pending_review")

### 4. Configure Permissions

Set the following permissions for the collection:

#### Create Permissions
- Any: Allow (for form submissions)

#### Read Permissions
- Users: Allow (for authenticated users to view their submissions)
- Admin Role: Allow (for admin dashboard)

#### Update Permissions
- Admin Role: Allow (for status updates)

#### Delete Permissions
- Admin Role: Allow (for data management)

## Environment Variables

Update your environment variables or Appwrite configuration:

```javascript
// In services/appwrite.js
const DATABASE_ID = '669b68a40003b8b5b8b8';
const INVESTORS_COLLECTION_ID = 'investors';
```

## Testing the Setup

1. Navigate to `/invest` page
2. Fill out the investor registration form
3. Submit the form
4. Check the Appwrite console to verify the document was created

## Data Structure Example

```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+**********",
  "dateOfBirth": "1985-06-15T00:00:00.000Z",
  "nationality": "American",
  "occupation": "business_owner",
  "company": "Tech Innovations Inc.",
  "jobTitle": "CEO",
  "workExperience": "11-15",
  "investmentExperience": "experienced",
  "investmentRange": "250k-500k",
  "riskTolerance": "moderate",
  "investmentGoals": "capital_growth",
  "preferredSectors": "[\"Technology\", \"Healthcare\", \"Fintech\"]",
  "annualIncome": "500k-1m",
  "netWorth": "1m-5m",
  "liquidAssets": "500k-1m",
  "hearAboutUs": "referral",
  "additionalComments": "Interested in early-stage startups",
  "agreeToTerms": true,
  "agreeToMarketing": false,
  "createdAt": "2024-01-15T10:30:00.000Z",
  "status": "pending_review"
}
```

## Admin Dashboard Integration

To view and manage investor registrations, you can:

1. Use the Appwrite Console directly
2. Build an admin dashboard using the provided service functions:
   - `appwriteService.getInvestorRegistrations()`
   - `appwriteService.updateInvestorStatus(documentId, status)`

## Security Considerations

1. Ensure proper rate limiting on form submissions
2. Validate all input data on both client and server side
3. Implement proper authentication for admin functions
4. Regular backup of investor data
5. Comply with data protection regulations (GDPR, CCPA, etc.)

## Troubleshooting

### Common Issues

1. **Permission Denied**: Check collection permissions
2. **Attribute Not Found**: Verify all attributes are created with correct names
3. **Database Not Found**: Ensure database ID matches configuration
4. **Collection Not Found**: Verify collection ID and name

### Debug Mode

Enable debug mode in development:

```javascript
// Add to services/appwrite.js for debugging
client.setEndpoint("https://cloud.appwrite.io/v1")
      .setProject("669b682f0002f3bf6887")
      .setSelfSigned(true); // Only for development
```
