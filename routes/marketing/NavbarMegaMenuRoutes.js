import { v4 as uuid } from 'uuid';


const NavbarMegaMenuRoutes = [
  {
    id: uuid(),
    menuitem: "Service",
    children: [
      {
        id: uuid(),
        menuitem: "Franchiseen",
        subtitle: "Finance",
        image: "/images/startup/franchiseen/logo.png",
        link: "/startup/franchiseen",
      },
      // {
      //   id: uuid(),
      //   menuitem: "Codelude",
      //   subtitle: "Software",
      //   image: "/images/startup/codelude/logo.png",
      //   link: "/startup/codelude",
      // },
      // {
      // 	id: uuid(),
      // 	menuitem: 'View all degree',
      // 	button : true,
      // 	link: '#'
      // }
    ],
  },
  {
    id: uuid(),
    menuitem: "Food",
    children: [
      {
        id: uuid(),
        menuitem: "Hubcv",
        subtitle: "Human Resource",
        image: "/images/startup/hubcv/logo.png",
        link: "/startup/hubcv",
      },
      // {
      //   id: uuid(),
      //   menuitem: "Chefless",
      //   subtitle: "Ready To Cook",
      //   image: "/images/startup/chefless/logo.png",
      //   link: "/startup/chefless",
      // },
      // {
      //   id: uuid(),
      //   menuitem: "Dietized",
      //   subtitle: "Health Kitchen",
      //   image: "/images/startup/dietized/logo.png",
      //   link: "/startup/dietized",
      // },
      // {
      //   id: uuid(),
      //   menuitem: "Biryani By MasterChef",
      //   subtitle: "Biryani",
      //   image: "/images/startup/mussles/logo.png",
      //   link: "/startup/mussles",
      // },
    ],
  },
  {
    id: uuid(),
    menuitem: "Retail",
    children: [
      {
        id: uuid(),
        menuitem: "Codelude",
        subtitle: "Software",
        image: "/images/startup/codelude/logo.png",
        link: "/startup/codelude",
      },
      // {
      //   id: uuid(),
      //   menuitem: "SXE",
      //   subtitle: "Luxury Fashion",
      //   image: "/images/startup/sxe/logo.png",
      //   link: "/startup/sxe",
      // },
      // {
      //   id: uuid(),
      //   menuitem: "SaudiChic",
      //   subtitle: "Arabic Fashion",
      //   image: "/images/startup/saudichic/logo.png",
      //   link: "/startup/cuestay",
      // },
    ],
  },
];

export default NavbarMegaMenuRoutes;
