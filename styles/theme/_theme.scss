// Components classes
@import "./components/_avatar.scss";
@import "./components/_button.scss";
@import "./components/_badge.scss";
@import "./components/_navbar.scss";
@import "./components/_footer.scss";
@import "./components/_nav.scss";
@import "./components/_course-layout.scss";
@import "./components/_sidenav.scss";
@import "./components/_table.scss";
@import "./components/_dropdown.scss";
@import "./components/_custom-forms.scss";
@import "./components/_admin-sidenav.scss";
@import "./components/_breadcrumb.scss";
@import "./components/_docs.scss";
@import "./components/_card.scss";
@import "./components/_reboot.scss";
@import "./components/_form.scss";
@import "./components/_password.scss";
@import "./components/_typography.scss";
@import "./components/_admin-structure.scss";
@import "./components/_social-button.scss";
@import "./components/_indicator.scss";
@import "./components/_ie.scss";
@import "./components/_grid.scss";
@import "./components/_gallery.scss";
@import "./components/_accordions.scss";
@import "./components/_progress.scss";
@import "./components/_mail.scss";
@import "./components/_task-kanban.scss";
@import "./components/_chat-app.scss";
@import "./components/_theme-switch.scss";
@import "./components/_pagination.scss";

// helpers classes
@import "./helpers/_text-truncate.scss";

// Vendor classes
@import "./vendor/odometer/_odometer.scss";
@import "./vendor/flatpickr/_flatpickr.scss";
@import "./vendor/quill/_quill-snow.scss";
@import "./vendor/prism/_prism.scss";
@import "./vendor/stepper/_stepper.scss";
@import "./vendor/apex-chart/_apexchart.scss";
@import "./vendor/tag-input/_tag-input.scss";
@import "./vendor/slick-slider/_slick_slider.scss";
@import "./vendor/tippyjs/_tippy.scss";
@import "./vendor/modal-video/_modal-video.scss";
@import "./vendor/react-toastify/main.scss";
@import "./vendor/nouislider/nouislider";
@import "./vendor/map/map";
@import "./vendor/lightbox/lightbox";
@import "./vendor/dropzone/_dropzone.scss";
@import "./vendor/fullcalendar/_calendar.scss";
@import "./vendor/bs-stepper/bs-stepper.scss";
