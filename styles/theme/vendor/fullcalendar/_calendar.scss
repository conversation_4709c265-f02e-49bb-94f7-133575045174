/* classes attached to <body> */
/* TODO: make fc-event selector work when calender in shadow DOM */

.fc-not-allowed {
    /* override events' custom cursors */
    cursor: not-allowed;

    .fc-event {
        /* override events' custom cursors */
        cursor: not-allowed;
    }
}

/* TODO: not attached to body. attached to specific els. move */

.fc-unselectable {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.fc {
    /* layout of immediate children */
    display: flex;
    flex-direction: column;
    font-size: 1em;
    box-sizing: border-box;

    * {
        box-sizing: border-box;

        &:before,
        &:after {
            box-sizing: border-box;
        }
    }

    table {
        border-collapse: collapse;
        border-spacing: 0;
        font-size: 1em;

        /* normalize cross-browser */
    }

    th {
        text-align: center;
        vertical-align: top;
        padding: 0;
        border-left: none !important;
        border-right: none !important;
    }

    td {
        vertical-align: top;
        padding: 0;
    }

    a[data-navlink] {
        cursor: pointer;
        color: var(--#{$prefix}gray-900);

        &:hover {
            text-decoration: underline;
        }
    }
}

.fc-direction-ltr {
    direction: ltr;
    text-align: left;
}

.fc-direction-rtl {
    direction: rtl;
    text-align: right;
}

.fc-theme-standard {
    td,
    th {
        border: 1px solid var(--#{$prefix}border-color);
    }
}

/* for FF, which doesn't expand a 100% div within a table cell. use absolute positioning */
/* inner-wrappers are responsible for being absolute */
/* TODO: best place for this? */

.fc-liquid-hack {
    td,
    th {
        position: relative;
    }
}

@font-face {
    font-family: "fcicons";
    src: url("data:application/x-font-ttf;charset=utf-8;base64,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")
        format("truetype");
    font-weight: normal;
    font-style: normal;
}

.fc-icon {
    /* added for fc */
    display: inline-block;
    width: 1em;
    height: 1em;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: "fcicons" !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.fc-icon-chevron-left:before {
    content: "\e900";
}

.fc-icon-chevron-right:before {
    content: "\e901";
}

.fc-icon-chevrons-left:before {
    content: "\e902";
}

.fc-icon-chevrons-right:before {
    content: "\e903";
}

.fc-icon-minus-square:before {
    content: "\e904";
}

.fc-icon-plus-square:before {
    content: "\e905";
}

.fc-icon-x:before {
    content: "\e906";
}

/*
  Lots taken from Flatly (MIT): https://bootswatch.com/4/flatly/bootstrap.css

  These styles only apply when the standard-theme is activated.
  When it's NOT activated, the fc-button classes won't even be in the DOM.
  */

.fc {
    /* reset */

    .fc-button {
        border-radius: 0;
        overflow: visible;
        text-transform: none;
        margin: 0;
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;

        &:focus {
            outline: 1px dotted;
            outline: 5px auto -webkit-focus-ring-color;
        }

        -webkit-appearance: button;

        &:not(:disabled) {
            cursor: pointer;
        }

        &::-moz-focus-inner {
            padding: 0;
            border-style: none;
        }

        display: inline-block;
        font-weight: 500;
        text-align: center;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-color: transparent;
        border: 1px solid transparent;
        padding: 0.25em 0.65em;
        font-size: 0.875rem;
        line-height: 1.9;
        border-radius: 0.25em;

        &:hover {
            text-decoration: none;
        }

        &:focus {
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
        }

        &:disabled {
            opacity: 0.65;
        }
    }

    /* theme */

    /* "primary" coloring */

    .fc-button-primary {
        color: var(--#{$prefix}gray-700);
        background-color: transparent;

        border-color: var(--#{$prefix}border-color);

        &:hover {
            color: var(--#{$prefix}gray-700);

            background-color: var(--#{$prefix}gray-300);

            border-color: var(--#{$prefix}border-color);
        }

        &:disabled {
            /* not DRY */
            color: var(--#{$prefix}gray-700);

            background-color: var(--#{$prefix}gray-300);

            border-color: var(--#{$prefix}border-color);

            /* overrides :hover */
        }

        &:focus {
            box-shadow: none;
        }

        &:not(:disabled) {
            &:active,
            &.fc-button-active {
                color: var(--#{$prefix}white);

                background-color: var(--#{$prefix}primary);

                border-color: var(--#{$prefix}primary);
            }

            &:active:focus,
            &.fc-button-active:focus {
                box-shadow: none;
            }
        }
    }

    /* icons within buttons */

    .fc-button .fc-icon {
        vertical-align: middle;
        font-size: 0.875rem;

        /* bump up the size (but don't make it bigger than line-height of button, which is 1.5em also) */
    }

    .fc-button-group {
        position: relative;
        display: inline-flex;
        vertical-align: middle;

        > .fc-button {
            position: relative;
            flex: 1 1 auto;

            &:hover,
            &:focus,
            &:active,
            &.fc-button-active {
                z-index: 1;
            }
        }
    }
}

.fc-direction-ltr .fc-button-group > .fc-button {
    &:not(:first-child) {
        margin-left: -1px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    &:not(:last-child) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
}

.fc-direction-rtl .fc-button-group > .fc-button {
    &:not(:first-child) {
        margin-right: -1px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    &:not(:last-child) {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
}

.fc {
    .fc-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;

        &.fc-header-toolbar {
            padding: 1.5rem 1.5rem;
            margin-bottom:0!important;
        }

        &.fc-footer-toolbar {
            margin-top: 1.5em;
        }
    }

    .fc-toolbar-title {
        font-size: 1.75em;
        margin: 0;
    }
}

.fc-direction-ltr .fc-toolbar > * > :not(:first-child) {
    margin-left: 0.75em;

    /* space between */
}

.fc-direction-rtl {
    .fc-toolbar > * > :not(:first-child) {
        margin-right: 0.75em;

        /* space between */
    }

    .fc-toolbar-ltr {
        /* when the toolbar-chunk positioning system is explicitly left-to-right */
        flex-direction: row-reverse;
    }
}

.fc {
    .fc-scroller {
        -webkit-overflow-scrolling: touch;
        position: relative;

        /* for abs-positioned elements within */
    }

    .fc-scroller-liquid {
        height: 100%;
    }

    .fc-scroller-liquid-absolute {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
    }

    .fc-scroller-harness {
        position: relative;
        overflow: hidden;
        direction: ltr;

        /* hack for chrome computing the scroller's right/left wrong for rtl. undone below... */
        /* TODO: demonstrate in codepen */
    }

    .fc-scroller-harness-liquid {
        height: 100%;
    }
}

.fc-direction-rtl .fc-scroller-harness > .fc-scroller {
    /* undo above hack */
    direction: rtl;
}

.fc-theme-standard .fc-scrollgrid {
    border: 1px solid var(--#{$prefix}border-color);
    border: 1px solid var(--#{$prefix}border-color);

    /* bootstrap does this. match */
}

.fc {
    .fc-scrollgrid {
        /* all tables (self included) */
        width: 100%;

        /* because tables don't normally do this */
        table-layout: fixed;

        table {
            /* all tables (self included) */
            width: 100%;

            /* because tables don't normally do this */
            table-layout: fixed;

            /* inner tables */
            border-top-style: hidden;
            border-left-style: hidden;
            border-right-style: hidden;
        }

        border-collapse: separate;
        border-right-width: 0;
        border-bottom-width: 0;
    }

    .fc-scrollgrid-liquid {
        height: 100%;
    }

    .fc-scrollgrid-section {
        /* a <tr> */
        height: 1px;

        /* better than 0, for firefox */

        > td {
            height: 1px;

            /* needs a height so inner div within grow. better than 0, for firefox */
        }

        table {
            height: 1px;

            /* for most browsers, if a height isn't set on the table, can't do liquid-height within cells */
            /* serves as a min-height. harmless */
        }
    }

    .fc-scrollgrid-section-liquid > td {
        height: 100%;

        /* better than `auto`, for firefox */
    }

    .fc-scrollgrid-section > * {
        border-top-width: 0;
        border-left-width: 0;
    }

    .fc-scrollgrid-section-header > *,
    .fc-scrollgrid-section-footer > * {
        border-bottom-width: 0;
    }

    .fc-scrollgrid-section-body table,
    .fc-scrollgrid-section-footer table {
        border-bottom-style: hidden;

        /* head keeps its bottom border tho */
    }

    /* stickiness */

    .fc-scrollgrid-section-sticky > * {
        background: var(--#{$prefix}white);

        position: sticky;
        z-index: 3;

        /* TODO: var */
        /* TODO: box-shadow when sticking */
    }

    .fc-scrollgrid-section-header.fc-scrollgrid-section-sticky > * {
        top: 0;

        /* because border-sharing causes a gap at the top */
        /* TODO: give safari -1. has bug */
    }

    .fc-scrollgrid-section-footer.fc-scrollgrid-section-sticky > * {
        bottom: 0;

        /* known bug: bottom-stickiness doesn't work in safari */
    }

    .fc-scrollgrid-sticky-shim {
        /* for horizontal scrollbar */
        height: 1px;

        /* needs height to create scrollbars */
        margin-bottom: -1px;
    }
}

.fc-sticky {
    /* no .fc wrap because used as child of body */
    position: sticky;
}

.fc {
    .fc-view-harness {
        flex-grow: 1;

        /* because this harness is WITHIN the .fc's flexbox */
        position: relative;
    }

    /* when the harness controls the height, make the view liquid */

    .fc-view-harness-active > .fc-view {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }

    .fc-col-header-cell-cushion {
        display: inline-block;
        /* x-browser for when sticky (when multi-tier header) */
        padding: 2px 4px;
        color: var(--#{$prefix}gray-900);
    }

    .fc-bg-event,
    .fc-non-business,
    .fc-highlight {
        /* will always have a harness with position:relative/absolute, so absolutely expand */
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .fc-non-business {
        background: var(--#{$prefix}gray-200);
    }

    .fc-bg-event {
        background: rgb(143, 223, 130);

        opacity: 0.3;

        .fc-event-title {
            margin: 0.5em;
            font-size: 0.85em;

            font-style: italic;
            font-weight: 500;
        }
    }

    .fc-highlight {
        background: var(--#{$prefix}light-info);
    }

    .fc-cell-shaded,
    .fc-day-disabled {
        background: var(--#{$prefix}gray-500);
    }
}

/* link resets */
/* ---------------------------------------------------------------------------------------------------- */

a.fc-event {
    text-decoration: none;

    &:hover {
        text-decoration: none;
    }
}

/* cursor */

.fc-event {
    &[href],
    &.fc-event-draggable {
        cursor: pointer;
    }

    .fc-event-main {
        position: relative;
        z-index: 2;
    }
}

/* event text content */
/* ---------------------------------------------------------------------------------------------------- */

/* dragging */
/* ---------------------------------------------------------------------------------------------------- */

.fc-event-dragging {
    &:not(.fc-event-selected) {
        /* MOUSE */
        opacity: 0.75;
    }

    &.fc-event-selected {
        /* TOUCH */
        box-shadow: 0 2px 7px rgba(0, 0, 0, 0.3);
    }
}

/* resizing */
/* ---------------------------------------------------------------------------------------------------- */
/* (subclasses should hone positioning for touch and non-touch) */

.fc-event {
    .fc-event-resizer {
        display: none;
        position: absolute;
        z-index: 4;
    }

    &:hover {
        /* TOUCH */
    }
}

.fc-event-selected {
    /* TOUCH */
}

.fc-event:hover .fc-event-resizer {
    display: block;
}

.fc-event-selected {
    .fc-event-resizer {
        display: block;
        border-radius: 4px;

        border-width: 1px;

        width: 8px;

        height: 8px;

        border-style: solid;
        border-color: inherit;
        background: var(--#{$prefix}white);

        /* expand hit area */

        &:before {
            content: "";
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
        }
    }

    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

    /* expand hit area (subclasses should expand) */
}

/* selecting (always TOUCH) */
/* OR, focused by tab-index */
/* (TODO: maybe not the best focus-styling for .fc-daygrid-dot-event) */
/* ---------------------------------------------------------------------------------------------------- */

.fc-event:focus {
    box-shadow: $box-shadow-smooth-sm;

    /* expand hit area (subclasses should expand) */
}

.fc-event-selected:before,
.fc-event:focus:before {
    content: "";
    position: absolute;
    z-index: 3;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.fc-event-selected,
.fc-event:focus {
    /* dimmer effect */
}

.fc-event-selected:after,
.fc-event:focus:after {
    content: "";
    background: rgba(0, 0, 0, 0.25);

    position: absolute;
    z-index: 1;

    /* assume there's a border on all sides. overcome it. */
    /* sometimes there's NOT a border, in which case the dimmer will go over */
    /* an adjacent border, which looks fine. */
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
}

/*
  A HORIZONTAL event
  */

.fc-h-event {
    /* allowed to be top-level */
    display: block;
    border: none; // 1px solid var(--#{$prefix}primary);

    background-color: var(--#{$prefix}primary);

    .fc-event-main {
        color: var(--#{$prefix}white);
    }

    .fc-event-main-frame {
        display: flex;

        /* for make fc-event-title-container expand */
    }

    .fc-event-time {
        max-width: 100%;

        /* clip overflow on this element */
        overflow: hidden;
    }

    .fc-event-title-container {
        /* serves as a container for the sticky cushion */
        flex-grow: 1;
        flex-shrink: 1;
        min-width: 0;

        /* important for allowing to shrink all the way */
    }

    .fc-event-title {
        display: inline-block;

        /* need this to be sticky cross-browser */
        vertical-align: top;

        /* for not messing up line-height */
        left: 0;

        /* for sticky */
        right: 0;

        /* for sticky */
        max-width: 100%;

        /* clip overflow on this element */
        overflow: hidden;
    }

    &.fc-event-selected:before {
        /* expand hit area */
        top: -10px;
        bottom: -10px;
    }
}

/* adjust border and border-radius (if there is any) for non-start/end */

.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-start),
.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-end) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left-width: 0;
}

.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-end),
.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-start) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right-width: 0;
}

/* resizers */

.fc-h-event:not(.fc-event-selected) .fc-event-resizer {
    top: 0;
    bottom: 0;
    width: 8px;
}

.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start,
.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end {
    cursor: w-resize;
    left: -4px;
}

.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end,
.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start {
    cursor: e-resize;
    right: -4px;
}

/* resizers for TOUCH */

.fc-h-event.fc-event-selected .fc-event-resizer {
    top: 50%;
    margin-top: -4px;
}

.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-start,
.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-end {
    left: -4px;
}

.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-end,
.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-start {
    right: -4px;
}

.fc {
    .fc-popover {
        position: absolute;
        z-index: 9999;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }

    .fc-popover-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 3px 4px;
    }

    .fc-popover-title {
        margin: 0 2px;
    }

    .fc-popover-close {
        cursor: pointer;
        opacity: 0.65;
        font-size: 1.1em;
    }
}

.fc-theme-standard {
    .fc-popover {
        border: 0;

        background: var(--#{$prefix}white);
        box-shadow: $box-shadow-smooth-sm;
        border-radius: 0.25rem;
    }

    .fc-popover-header {
        background: var(--#{$prefix}gray-200);
        border-start-end-radius: 0.25rem;
        border-start-start-radius: 0.25rem;
    }
}

/* help things clear margins of inner content */

.fc-daygrid-day-frame,
.fc-daygrid-day-events,
.fc-daygrid-event-harness {
    /* for event top/bottom margins */
}

.fc-daygrid-day-frame:before,
.fc-daygrid-day-events:before,
.fc-daygrid-event-harness:before,
.fc-daygrid-day-frame:after,
.fc-daygrid-day-events:after,
.fc-daygrid-event-harness:after {
    content: "";
    clear: both;
    display: table;
}

.fc {
    .fc-daygrid-body {
        /* a <div> that wraps the table */
        position: relative;
        z-index: 1;

        /* container inner z-index's because <tr>s can't do it */
    }

    .fc-daygrid-day.fc-day-today {
        background-color: var(--#{$prefix}light-warning);
    }

    .fc-daygrid-day-frame {
        position: relative;
        min-height: 100%;

        /* seems to work better than `height` because sets height after rows/cells naturally do it */
    }

    /* cell top */

    .fc-daygrid-day-top {
        display: flex;
        flex-direction: row-reverse;
    }

    .fc-day-other .fc-daygrid-day-top {
        opacity: 0.3;
    }

    /* day number (within cell top) */

    .fc-daygrid-day-number {
        position: relative;
        z-index: 4;
        padding: 4px;
    }

    /* event container */

    .fc-daygrid-day-events {
        margin-top: 1px;

        /* needs to be margin, not padding, so that available cell height can be computed */
    }

    /* positioning for balanced vs natural */

    .fc-daygrid-body-balanced .fc-daygrid-day-events {
        position: absolute;
        left: 0;
        right: 0;
    }

    .fc-daygrid-body-unbalanced .fc-daygrid-day-events {
        position: relative;

        /* for containing abs positioned event harnesses */
        min-height: 2em;

        /* in addition to being a min-height during natural height, equalizes the heights a little bit */
    }

    .fc-daygrid-body-natural {
        /* can coexist with -unbalanced */

        .fc-daygrid-day-events {
            margin-bottom: 1em;
        }
    }

    /* event harness */

    .fc-daygrid-event-harness {
        position: relative;
    }

    .fc-daygrid-event-harness-abs {
        position: absolute;
        top: 0;

        /* fallback coords for when cannot yet be computed */
        left: 0;

        /* */
        right: 0;

        /* */
    }

    .fc-daygrid-bg-harness {
        position: absolute;
        top: 0;
        bottom: 0;
    }

    /* bg content */

    .fc-daygrid-day-bg {
        .fc-non-business {
            z-index: 1;
        }

        .fc-bg-event {
            z-index: 2;
        }

        .fc-highlight {
            z-index: 3;
        }
    }

    /* events */

    .fc-daygrid-event {
        z-index: 6;
        margin-top: 1px;

        &.fc-event-mirror {
            z-index: 7;
        }
    }

    /* cell bottom (within day-events) */

    .fc-daygrid-day-bottom {
        font-size: 0.85em;
        padding: 2px 3px 0;

        &:before {
            content: "";
            clear: both;
            display: table;
        }
    }

    .fc-daygrid-more-link {
        position: relative;
        z-index: 4;
        cursor: pointer;
    }

    /* week number (within frame) */

    .fc-daygrid-week-number {
        position: absolute;
        z-index: 5;
        top: 0;
        padding: 2px;
        min-width: 1.5em;
        text-align: center;
        background-color: var(--#{$prefix}gray-100);

        color: var(--#{$prefix}gray-700);
    }

    /* popover */

    .fc-more-popover .fc-popover-body {
        min-width: 220px;
        padding: 10px;
    }
}

.fc-direction-ltr .fc-daygrid-event.fc-event-start,
.fc-direction-rtl .fc-daygrid-event.fc-event-end {
    margin-left: 2px;
}

.fc-direction-ltr .fc-daygrid-event.fc-event-end,
.fc-direction-rtl .fc-daygrid-event.fc-event-start {
    margin-right: 2px;
}

.fc-direction-ltr .fc-daygrid-week-number {
    left: 0;
    border-radius: 0 0 3px 0;
}

.fc-direction-rtl .fc-daygrid-week-number {
    right: 0;
    border-radius: 0 0 0 3px;
}

.fc-liquid-hack .fc-daygrid-day-frame {
    position: static;

    /* will cause inner absolute stuff to expand to <td> */
}

.fc-daygrid-event {
    /* make root-level, because will be dragged-and-dropped outside of a component root */
    position: relative;

    /* for z-indexes assigned later */
    white-space: nowrap;
    border-radius: 3px;

    /* dot event needs this to when selected */
    font-size: 0.85em;
}

/* --- the rectangle ("block") style of event --- */

.fc-daygrid-block-event {
    .fc-event-time {
        font-weight: bold;
        padding: 1px;
    }

    .fc-event-title {
        padding: 4px 6px;
        line-height: 1;
        font-weight: 500;
    }
}

/* --- the dot style of event --- */

.fc-daygrid-dot-event {
    display: flex;
    align-items: center;
    padding: 2px 4px;
    background-color: var(--#{$prefix}light-primary);

    .fc-event-title {
        flex-grow: 1;
        flex-shrink: 1;
        min-width: 0;
        overflow: hidden;
        font-weight: bold;
    }

    &:hover,
    &.fc-event-mirror {
        background-color: var(--#{$prefix}light-primary);
    }

    &.fc-event-selected:before {
        /* expand hit area */
        top: -10px;
        bottom: -10px;
    }
}

.fc-daygrid-event-dot {
    /* the actual dot */
    margin: 0 4px;
    box-sizing: content-box;
    width: 0;
    height: 0;
    border: 4px solid var(--#{$prefix}dark-primary);
    border-radius: 4px;
}

/* --- spacing between time and title --- */

.fc-direction-ltr .fc-daygrid-event .fc-event-time {
    margin-right: 3px;
}

.fc-direction-rtl .fc-daygrid-event .fc-event-time {
    margin-left: 3px;
}

/*
  A VERTICAL event
  */

.fc-v-event {
    /* allowed to be top-level */
    display: block;
    border: 1px solid var(--#{$prefix}primary);

    background-color: var(--#{$prefix}primary);

    .fc-event-main {
        color: var(--#{$prefix}white);

        height: 100%;
    }

    .fc-event-main-frame {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .fc-event-time {
        flex-grow: 0;
        flex-shrink: 0;
        max-height: 100%;
        overflow: hidden;
    }

    .fc-event-title-container {
        /* a container for the sticky cushion */
        flex-grow: 1;
        flex-shrink: 1;
        min-height: 0;

        /* important for allowing to shrink all the way */
    }

    .fc-event-title {
        /* will have fc-sticky on it */
        top: 0;
        bottom: 0;
        max-height: 100%;

        /* clip overflow */
        overflow: hidden;
    }

    &:not(.fc-event-start) {
        border-top-width: 0;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
    }

    &:not(.fc-event-end) {
        border-bottom-width: 0;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
    }

    &.fc-event-selected:before {
        /* expand hit area */
        left: -10px;
        right: -10px;
    }

    /* resizer (mouse AND touch) */

    .fc-event-resizer-start {
        cursor: n-resize;
    }

    .fc-event-resizer-end {
        cursor: s-resize;
    }

    /* resizer for MOUSE */

    &:not(.fc-event-selected) {
        .fc-event-resizer {
            height: 8px;

            left: 0;
            right: 0;
        }

        .fc-event-resizer-start {
            top: -4px;
        }

        .fc-event-resizer-end {
            bottom: -4px;
        }
    }

    /* resizer for TOUCH (when event is "selected") */

    &.fc-event-selected {
        .fc-event-resizer {
            left: 50%;
            margin-left: -4px;
        }

        .fc-event-resizer-start {
            top: -4px;
        }

        .fc-event-resizer-end {
            bottom: -4px;
        }
    }
}

.fc {
    .fc-timegrid .fc-daygrid-body {
        /* the all-day daygrid within the timegrid view */
        z-index: 2;

        /* put above the timegrid-body so that more-popover is above everything. TODO: better solution */
    }

    .fc-timegrid-divider {
        padding: 0 0 2px;

        /* browsers get confused when you set height. use padding instead */
    }

    .fc-timegrid-body {
        position: relative;
        z-index: 1;

        /* scope the z-indexes of slots and cols */
        min-height: 100%;

        /* fill height always, even when slat table doesn't grow */
    }

    .fc-timegrid-axis-chunk {
        /* for advanced ScrollGrid */
        position: relative;

        /* offset parent for now-indicator-container */

        > table {
            position: relative;
            z-index: 1;

            /* above the now-indicator-container */
        }
    }

    .fc-timegrid-slots {
        position: relative;
        z-index: 1;
    }

    .fc-timegrid-slot {
        /* a <td> */
        height: 1.5em;
        border-bottom: 0;

        /* each cell owns its top border */

        &:empty:before {
            content: "\00a0";

            /* make sure there's at least an empty space to create height for height syncing */
        }
    }

    .fc-timegrid-slot-minor {
        border-top-style: dotted;
    }

    .fc-timegrid-slot-label-cushion {
        display: inline-block;
        white-space: nowrap;
    }

    .fc-timegrid-slot-label {
        vertical-align: middle;

        /* vertical align the slots */
    }

    /* slots AND axis cells (top-left corner of view including the "all-day" text) */

    .fc-timegrid-axis-cushion,
    .fc-timegrid-slot-label-cushion {
        padding: 0 4px;
    }

    /* axis cells (top-left corner of view including the "all-day" text) */
    /* vertical align is more complicated, uses flexbox */

    .fc-timegrid-axis-frame-liquid {
        height: 100%;

        /* will need liquid-hack in FF */
    }

    .fc-timegrid-axis-frame {
        overflow: hidden;
        display: flex;
        align-items: center;

        /* vertical align */
        justify-content: flex-end;

        /* horizontal align. matches text-align below */
    }

    .fc-timegrid-axis-cushion {
        max-width: 60px;

        /* limits the width of the "all-day" text */
        flex-shrink: 0;

        /* allows text to expand how it normally would, regardless of constrained width */
    }
}

.fc-direction-ltr .fc-timegrid-slot-label-frame {
    text-align: right;
}

.fc-direction-rtl .fc-timegrid-slot-label-frame {
    text-align: left;
}

.fc-liquid-hack .fc-timegrid-axis-frame-liquid {
    height: auto;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.fc {
    .fc-timegrid-col.fc-day-today {
        background-color: var(--#{$prefix}light-warning);
    }

    .fc-timegrid-col-frame {
        min-height: 100%;

        /* liquid-hack is below */
        position: relative;
    }
}

.fc-media-screen {
    &.fc-liquid-hack .fc-timegrid-col-frame {
        height: auto;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }

    .fc-timegrid-cols {
        position: absolute;

        /* no z-index. children will decide and go above slots */
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;

        > table {
            height: 100%;
        }
    }

    .fc-timegrid-col-bg,
    .fc-timegrid-col-events,
    .fc-timegrid-now-indicator-container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
    }
}

.fc {
    /* bg */

    .fc-timegrid-col-bg {
        z-index: 2;

        /* TODO: kill */

        .fc-non-business {
            z-index: 1;
        }

        .fc-bg-event {
            z-index: 2;
        }

        .fc-highlight {
            z-index: 3;
        }
    }

    .fc-timegrid-bg-harness {
        position: absolute;

        /* top/bottom will be set by JS */
        left: 0;
        right: 0;
    }

    /* fg events */
    /* (the mirror segs are put into a separate container with same classname, */
    /* and they must be after the normal seg container to appear at a higher z-index) */

    .fc-timegrid-col-events {
        z-index: 3;

        /* child event segs have z-indexes that are scoped within this div */
    }

    /* now indicator */

    .fc-timegrid-now-indicator-container {
        bottom: 0;
        overflow: hidden;

        /* don't let overflow of lines/arrows cause unnecessary scrolling */
        /* z-index is set on the individual elements */
    }
}

.fc-direction-ltr .fc-timegrid-col-events {
    margin: 0 2.5% 0 2px;
}

.fc-direction-rtl .fc-timegrid-col-events {
    margin: 0 2px 0 2.5%;
}

.fc-timegrid-event-harness {
    position: absolute;

    /* top/left/right/bottom will all be set by JS */

    > .fc-timegrid-event {
        position: absolute;

        /* absolute WITHIN the harness */
        top: 0;

        /* for when not yet positioned */
        bottom: 0;

        /* " */
        left: 0;
        right: 0;
    }
}

.fc-timegrid-event-harness-inset .fc-timegrid-event,
.fc-timegrid-event.fc-event-mirror,
.fc-timegrid-more-link {
    box-shadow: 0px 0px 0px 1px var(--#{$prefix}white);
}

.fc-timegrid-event,
.fc-timegrid-more-link {
    /* events need to be root */
    font-size: 0.85em;

    border-radius: 3px;
}

.fc-timegrid-event {
    /* events need to be root */
    margin-bottom: 1px;

    /* give some space from bottom */

    .fc-event-main {
        padding: 1px 1px 0;
    }

    .fc-event-time {
        white-space: nowrap;
        font-size: 0.85em;

        margin-bottom: 1px;
    }
}

.fc-timegrid-event-short {
    .fc-event-main-frame {
        flex-direction: row;
        overflow: hidden;
    }

    .fc-event-time:after {
        content: "\00a0-\00a0";

        /* dash surrounded by non-breaking spaces */
    }

    .fc-event-title {
        font-size: 0.85em;
    }
}

.fc-timegrid-more-link {
    /* does NOT inherit from fc-timegrid-event */
    position: absolute;
    z-index: 9999;

    /* hack */
    color: inherit;

    background: var(--#{$prefix}gray-100);

    cursor: pointer;
    margin-bottom: 1px;

    /* match space below fc-timegrid-event */
}

.fc-timegrid-more-link-inner {
    /* has fc-sticky */
    padding: 3px 2px;
    top: 0;
}

.fc-direction-ltr .fc-timegrid-more-link {
    right: 0;
}

.fc-direction-rtl .fc-timegrid-more-link {
    left: 0;
}

.fc {
    /* line */

    .fc-timegrid-now-indicator-line {
        position: absolute;
        z-index: 4;
        left: 0;
        right: 0;
        border-style: solid;
        border-color: var(--#{$prefix}danger);

        border-width: 1px 0 0;
    }

    /* arrow */

    .fc-timegrid-now-indicator-arrow {
        position: absolute;
        z-index: 4;
        margin-top: -5px;

        /* vertically center on top coordinate */
        border-style: solid;
        border-color: var(--#{$prefix}danger);
    }
}

.fc-direction-ltr .fc-timegrid-now-indicator-arrow {
    left: 0;

    /* triangle pointing right. TODO: mixin */
    border-width: 5px 0 5px 6px;
    border-top-color: transparent;
    border-bottom-color: transparent;
}

.fc-direction-rtl .fc-timegrid-now-indicator-arrow {
    right: 0;

    /* triangle pointing left. TODO: mixin */
    border-width: 5px 6px 5px 0;
    border-top-color: transparent;
    border-bottom-color: transparent;
}

.fc-theme-standard .fc-list {
    border: 1px solid var(--#{$prefix}border-color);
}

.fc {
    /* message when no events */

    .fc-list-empty {
        background-color: var(--#{$prefix}gray-200);

        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        /* vertically aligns fc-list-empty-inner */
    }

    .fc-list-empty-cushion {
        margin: 5em 0;
    }

    /* table within the scroller */
    /* ---------------------------------------------------------------------------------------------------- */

    .fc-list-table {
        width: 100%;
        border-style: hidden;

        /* kill outer border on theme */

        tr > * {
            border-left: 0;
            border-right: 0;
        }
    }

    .fc-list-sticky .fc-list-day > * {
        /* the cells */
        position: sticky;
        top: 0;
        background: var(--#{$prefix}white);

        /* for when headers are styled to be transparent and sticky */
    }

    /* only exists for aria reasons, hide for non-screen-readers */

    .fc-list-table {
        thead {
            position: absolute;
            left: -10000px;
        }

        tbody > tr:first-child th {
            border-top: 0;
        }

        th {
            padding: 0;

            /* uses an inner-wrapper instead... */
        }

        td {
            padding: 8px 14px;
        }
    }

    /* the table's border-style:hidden gets confused by hidden thead. force-hide top border of first cell */

    .fc-list-day-cushion {
        padding: 8px 14px;

        &:after {
            content: "";
            clear: both;
            display: table;

            /* clear floating */
        }
    }

    /* date heading rows */
    /* ---------------------------------------------------------------------------------------------------- */
}

.fc-theme-standard .fc-list-day-cushion {
    background-color: var(--#{$prefix}gray-100);
}

.fc-direction-ltr .fc-list-day-text,
.fc-direction-rtl .fc-list-day-side-text {
    float: left;
}

.fc-direction-ltr .fc-list-day-side-text,
.fc-direction-rtl .fc-list-day-text {
    float: right;
}

/* make the dot closer to the event title */

.fc-direction-ltr .fc-list-table .fc-list-event-graphic {
    padding-right: 0;
}

.fc-direction-rtl .fc-list-table .fc-list-event-graphic {
    padding-left: 0;
}

.fc {
    .fc-list-event {
        &.fc-event-forced-url {
            cursor: pointer;

            /* whole row will seem clickable */
        }

        &:hover td {
            background-color: var(--#{$prefix}gray-100);
        }
    }

    /* shrink certain cols */

    .fc-list-event-graphic,
    .fc-list-event-time {
        white-space: nowrap;
        width: 1px;
    }

    .fc-list-event-dot {
        display: inline-block;
        box-sizing: content-box;
        width: 0;
        height: 0;
        border: 5px solid var(--#{$prefix}primary);

        border-radius: 5px;
    }

    /* reset <a> styling */

    .fc-list-event-title a {
        color: inherit;
        text-decoration: none;
    }

    /* underline link when hovering over any part of row */

    .fc-list-event.fc-event-forced-url:hover a {
        text-decoration: underline;
    }
}

.fc-theme-bootstrap a:not([href]) {
    color: inherit;

    /* natural color for navlinks */
}

@media (max-width: 768px) {
    .fc {
        .fc-header-toolbar {
            flex-direction: column;
            align-items: flex-start;
        }
        .fc-toolbar-chunk:nth-child(1),
        .fc-toolbar-chunk:nth-child(2) {
            margin-bottom: 0.5rem;
        }
    }
}
