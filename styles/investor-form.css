/* Typeform-style Investor Registration Form Styles */

.investor-form-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.form-step-card {
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
}

.form-step-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.progress-bar {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.5s ease;
}

.progress-bar-container {
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-outline-secondary {
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.step-title {
  color: #2d3748;
  font-weight: 700;
  margin-bottom: 8px;
}

.step-subtitle {
  color: #718096;
  font-size: 1.1rem;
  margin-bottom: 0;
}

.form-label {
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 8px;
}

.form-control,
.form-select {
  border-radius: 8px;
  border: 2px solid #e2e8f0;
  padding: 12px 16px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-control:hover,
.form-select:hover {
  border-color: #cbd5e0;
}

.form-check-input:checked {
  background-color: #667eea;
  border-color: #667eea;
}

.form-check-label {
  color: #4a5568;
  font-weight: 500;
}

.success-icon {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.alert-info {
  background-color: #ebf8ff;
  border-color: #bee3f8;
  color: #2b6cb0;
  border-radius: 8px;
}

.navigation-buttons {
  background-color: #f7fafc;
  border-top: 1px solid #e2e8f0;
}

/* Animation for step transitions */
.step-content {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-step-card {
    margin: 10px;
    border-radius: 12px;
  }
  
  .step-title {
    font-size: 1.5rem;
  }
  
  .step-subtitle {
    font-size: 1rem;
  }
}

/* Custom checkbox styles for sectors */
.form-check {
  margin-bottom: 12px;
}

.form-check-input {
  margin-top: 0.25rem;
}

.form-check-label {
  margin-left: 8px;
  cursor: pointer;
}

/* Progress indicator styles */
.progress-indicator {
  font-size: 0.875rem;
  font-weight: 600;
  color: #718096;
}

/* Success page styles */
.success-container {
  text-align: center;
  padding: 3rem 2rem;
}

.success-container h2 {
  color: #2d3748;
  font-weight: 700;
  margin-bottom: 1rem;
}

.success-container p {
  color: #718096;
  font-size: 1.1rem;
  line-height: 1.6;
}
