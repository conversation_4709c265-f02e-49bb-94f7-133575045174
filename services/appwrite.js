// import node module libraries
import { Client, Account, Databases, ID } from 'appwrite';

// Create Appwrite client
const client = new Client();

client
  .setEndpoint("https://cloud.appwrite.io/v1")
  .setProject("669b682f0002f3bf6887");

// Create account service
const account = new Account(client);

// Create database service
const databases = new Databases(client);

// Database configuration
const DATABASE_ID = '669b68a40003b8b5b8b8'; // You'll need to create this database in Appwrite
const INVESTORS_COLLECTION_ID = 'investors'; // You'll need to create this collection

// Authentication service functions
export const appwriteService = {
  // Sign up new user
  async signUp(email, password, name) {
    try {
      const response = await account.create(ID.unique(), email, password, name);
      
      // After successful registration, create a session
      await this.signIn(email, password);
      
      return response;
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  },

  // Sign in user
  async signIn(email, password) {
    try {
      const response = await account.createEmailPasswordSession(email, password);
      return response;
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  },

  // Sign out user
  async signOut() {
    try {
      await account.deleteSession('current');
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  },

  // Get current user
  async getCurrentUser() {
    try {
      const user = await account.get();
      return user;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  },

  // Send password recovery email
  async sendPasswordRecovery(email) {
    try {
      const response = await account.createRecovery(
        email,
        `${window.location.origin}/authentication/reset-password`
      );
      return response;
    } catch (error) {
      console.error('Password recovery error:', error);
      throw error;
    }
  },

  // Complete password recovery
  async completePasswordRecovery(userId, secret, password) {
    try {
      const response = await account.updateRecovery(userId, secret, password);
      return response;
    } catch (error) {
      console.error('Complete password recovery error:', error);
      throw error;
    }
  },

  // Check if user is authenticated
  async isAuthenticated() {
    try {
      await account.get();
      return true;
    } catch (error) {
      return false;
    }
  },

  // Database operations for investor registration
  async createInvestorRegistration(investorData) {
    try {
      const response = await databases.createDocument(
        DATABASE_ID,
        INVESTORS_COLLECTION_ID,
        ID.unique(),
        {
          ...investorData,
          createdAt: new Date().toISOString(),
          status: 'pending_review'
        }
      );
      return response;
    } catch (error) {
      console.error('Create investor registration error:', error);
      throw error;
    }
  },

  // Get investor registrations (for admin use)
  async getInvestorRegistrations() {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        INVESTORS_COLLECTION_ID
      );
      return response;
    } catch (error) {
      console.error('Get investor registrations error:', error);
      throw error;
    }
  },

  // Update investor registration status
  async updateInvestorStatus(documentId, status) {
    try {
      const response = await databases.updateDocument(
        DATABASE_ID,
        INVESTORS_COLLECTION_ID,
        documentId,
        { status }
      );
      return response;
    } catch (error) {
      console.error('Update investor status error:', error);
      throw error;
    }
  }
};

export { client, account, databases, DATABASE_ID, INVESTORS_COLLECTION_ID };
export default appwriteService;
